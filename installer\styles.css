body {
	background: white;
	font-family: "Lucida Grande", Verdana, Arial, Helvetica, sans-serif;
	font-size: small;
	color: black;
	margin: 0;
}

#banner {
	position: relative;
	height: 58px;
	margin: 0 0 1em 0;
	padding: 10px 20px;
	background: url('images/banner_gradient.gif') top left repeat-x #d8edfd;
	overflow: hidden;
}

#banner .banner-bg {
	position: absolute;
	top: 0;
	right: 0;
	width: 630px;
	height: 78px;
	background: url('images/banner_schraffur.gif') top right no-repeat;
	z-index: 0;
}

#banner .banner-logo {
	position: absolute;
	top: 10px;
	left: 20px;
	z-index: 4;
}

#banner .banner-logo a {
	border: 0;
}

#topnav {
	position: absolute;
	top: 3.6em;
	right: 20px;
}

#topnav a {
	color: #666;
}

#content {
	margin: 2em 20px;
}

#footer {
  margin: 2em 20px 1em 20px;
  padding-top: 0.6em;
  font-size: smaller;
  text-align: center;
  border-top: 1px dotted #999;
}

#progress {
  margin-bottom: 2em;
  border: 1px solid #aaa;
  background-color: #f9f9f9;
}

#progress:after {
  content: ".";
  display: block;
  height: 0;
  font-size: 0;
  clear: both;
  visibility: hidden;
}

#progress li {
  float: left;
  color: #999;
  padding: 1em 5em 1em 0.2em;
}

#progress li a {
  color: #999;
  text-decoration: none;
}

#progress li a:hover {
  text-decoration: underline;
}

#progress li.current {
  color: #000;
  font-weight: bold;
}

#progress li.passed,
#progress li.passed a,
#progress li.current a {
  color: #333;
}

fieldset {
  margin-bottom: 1.5em;
  border: 1px solid #aaa;
  background-color: #f9f9f9;
}

fieldset p.hint {
  margin-top: 0.5em;
}

legend {
  font-size: 1.1em;
  font-weight: bold;
}

textarea.configfile {
  background-color: #f9f9f9;
  font-family: monospace;
  font-size: 9pt;
  width: 100%;
  height: 30em;
}

.propname {
  font-family: monospace;
  font-size: 9pt;
  margin-top: 1em;
  margin-bottom: 0.6em;
}

dd div {
  margin-top: 0.3em;
}

dd label {
  padding-left: 0.5em;
}

th {
  text-align: left;
}

td > label {
    min-width: 6em;
    display: inline-block;
}

ul li {
  margin: 0.3em 0 0.4em -1em;
}

ul li ul li {
  margin-bottom: 0.2em;
}

h3 {
  font-size: 1.1em;
  margin-top: 1.5em;
  margin-bottom: 0.6em;
}

h4 {
  margin-bottom: 0.2em;
}

a.blocktoggle {
  color: #666;
  text-decoration: none;
}

a.addlink {
  color: #999;
  font-size: 0.9em;
  padding: 1px 0 1px 20px;
  background: url('images/add.png') top left no-repeat;
  text-decoration: none;
}

a.removelink {
  color: #999;
  font-size: 0.9em;
  padding: 1px 0 1px 24px;
  background: url('images/delete.png') 4px 0 no-repeat;
  text-decoration: none;
}

.hint {
  color: #666;
  font-size: 0.95em;
}

.success {
  color: #006400;
  font-weight: bold !important;
}

.fail {
  color: #ff0000 !important;
  font-weight: bold !important;
}

.na {
  color: #f60;
  font-weight: bold;
}

.indent {
  padding-left: 0.8em;
}

.notice {
  padding: 1em;
  background-color: #f7fdcb;
  border: 2px solid #c2d071;
}

.suggestion {
  padding: 0.6em;
  background-color: #ebebeb;
  border: 1px solid #999;
}

p.warning,
div.warning {
  padding: 1em;
  background-color: #ef9398;
  border: 2px solid #dc5757;
}

h3.warning {
  color: #c00;
  background: url('images/error.png') top left no-repeat;
  padding-left: 24px;
}

.userconf {
  color: #00c;
  font-family: "Lucida Grande", Verdana, Arial, Helvetica, sans-serif;
}
