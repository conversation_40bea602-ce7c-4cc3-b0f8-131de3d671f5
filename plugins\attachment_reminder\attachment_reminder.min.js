/**
 * Attachment Reminder plugin script
 *
 * @licstart  The following is the entire license notice for the
 * JavaScript code in this file.
 *
 * Copyright (c) The Roundcube Dev Team
 *
 * The JavaScript code in this page is free software: you can redistribute it
 * and/or modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version.
 *
 * @licend  The above is the entire license notice
 * for the JavaScript code in this file.
 */
function rcmail_get_compose_message(){var e=rcmail.editor.get_content({nosig:!0});return e=rcmail.editor.is_html()?e.replace(/<blockquote[^>]*>(.|[\r\n])*<\/blockquote>/gim,"").replace(/<[^>]+>/gm," ").replace(/&nbsp;/g," "):e.replace(/^>.*$/gim,"")}function rcmail_check_message(e){var a=rcmail.get_label("keywords","attachment_reminder").split(",").concat([".doc",".pdf"]),a=$.map(a,function(e){return RegExp.escape(e)}),a=new RegExp("("+a.join("|")+")","i");return-1!=e.search(a)}function rcmail_have_attachments(){return rcmail.env.attachments&&$("li",rcmail.gui_objects.attachmentlist).length}function rcmail_attachment_reminder_dialog(){var e={};e[rcmail.get_label("addattachment")]=function(){$(this).remove(),$("#messagetoolbar a.attach, .toolbar a.attach").first().click()},e[rcmail.get_label("send")]=function(e){$(this).remove(),rcmail.env.attachment_reminder=!0,rcmail.command("send","",e)},rcmail.env.attachment_reminder=!1,rcmail.show_popup_dialog(rcmail.get_label("attachment_reminder.forgotattachment"),rcmail.get_label("attachment_reminder.missingattachment"),e,{button_classes:["mainaction attach","send"]})}window.rcmail&&rcmail.addEventListener("beforesend",function(e){var a=rcmail_get_compose_message(),t=$("#compose-subject").val();if(!rcmail.env.attachment_reminder&&!rcmail_have_attachments()&&(rcmail_check_message(a)||rcmail_check_message(t)))return rcmail_attachment_reminder_dialog(),!1});
