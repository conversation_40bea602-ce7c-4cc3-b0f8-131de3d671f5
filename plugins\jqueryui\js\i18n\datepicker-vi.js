/* Vietnamese initialisation for the jQuery UI date picker plugin. */
/* Translated by <PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.vi = {
	closeText: "Đóng",
	prevText: "Tr<PERSON>ớ<PERSON>",
	nextText: "Tiếp",
	currentText: "Hôm nay",
	monthNames: [ "<PERSON>h<PERSON><PERSON>", "<PERSON>h<PERSON><PERSON>", "Th<PERSON>g Ba", "<PERSON><PERSON><PERSON>g <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>h<PERSON><PERSON>",
	"<PERSON>h<PERSON><PERSON>", "<PERSON>hán<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>" ],
	monthNamesShort: [ "Tháng 1", "Tháng 2", "Tháng 3", "<PERSON>h<PERSON><PERSON> 4", "<PERSON>h<PERSON>g 5", "<PERSON>h<PERSON>g 6",
	"<PERSON>háng 7", "Tháng 8", "<PERSON>h<PERSON>g 9", "Tháng 10", "<PERSON>háng 11", "Tháng 12" ],
	day<PERSON>ames: [ "Ch<PERSON> Nh<PERSON>t", "Th<PERSON> <PERSON>", "<PERSON>h<PERSON> <PERSON>", "Th<PERSON> T<PERSON>", "Th<PERSON> N<PERSON>m", "<PERSON>h<PERSON> <PERSON><PERSON>u", "Th<PERSON> <PERSON><PERSON>y" ],
	dayNamesShort: [ "CN", "T2", "T3", "T4", "T5", "T6", "T7" ],
	dayNamesMin: [ "CN", "T2", "T3", "T4", "T5", "T6", "T7" ],
	weekHeader: "Tu",
	dateFormat: "dd/mm/yy",
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.vi );

return datepicker.regional.vi;

} );
