/**
 * ACL plugin script
 *
 * @licstart  The following is the entire license notice for the
 * JavaScript code in this file.
 *
 * Copyright (c) The Roundcube Dev Team
 *
 * The JavaScript code in this page is free software: you can redistribute it
 * and/or modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version.
 *
 * @licend  The above is the entire license notice
 * for the JavaScript code in this file.
 */
window.rcmail&&rcmail.addEventListener("init",function(){var e;rcmail.gui_objects.acltable&&(rcmail.acl_list_init(),rcmail.env.acl_users_source)&&((e=rcmail.is_framed()?parent.rcmail:rcmail).init_address_input_events($("#acluser"),{action:"settings/plugin.acl-autocomplete"}),e.set_env({autocomplete_max:rcmail.env.autocomplete_max,autocomplete_min_length:rcmail.env.autocomplete_min_length}),e.add_label("autocompletechars",rcmail.labels.autocompletechars),e.add_label("autocompletemore",rcmail.labels.autocompletemore),e.addEventListener("autocomplete_insert",function(e){"acluser"==e.field.id&&(e.field.value=e.insert.replace(/[ ,;]+$/,""))})),rcmail.enable_command("acl-create","acl-save","acl-cancel","acl-mode-switch",!0),rcmail.enable_command("acl-delete","acl-edit",!1),rcmail.env.acl_advanced&&$("#acl-switch").addClass("selected").find("input").prop("checked",!0)}),rcube_webmail.prototype.acl_create=function(){this.acl_init_form()},rcube_webmail.prototype.acl_edit=function(){var e=this.acl_list.get_single_selection();e&&this.acl_init_form(e)},rcube_webmail.prototype.acl_delete=function(){var a=this.acl_get_usernames();a&&a.length&&this.confirm_dialog(this.get_label("acl.deleteconfirm"),"delete",function(e,t){t.http_post("settings/plugin.acl",{_act:"delete",_user:a.join(","),_mbox:rcmail.env.mailbox},t.set_busy(!0,"acl.deleting"))})},rcube_webmail.prototype.acl_save=function(){var e,t="",a=$("#acluser",this.acl_form).val();$(this.env.acl_advanced?"#advancedrights :checkbox":"#simplerights :checkbox",this.acl_form).map(function(){this.checked&&(t+=this.value)}),(a=(e=$("input:checked[name=usertype]",this.acl_form).val())&&"user"!=e?e:a)?t?(e={_act:"save",_user:a,_acl:t,_mbox:this.env.mailbox},this.acl_id&&(e._old=this.acl_id),this.http_post("settings/plugin.acl",e,this.set_busy(!0,"acl.saving"))):this.alert_dialog(this.get_label("acl.norights")):this.alert_dialog(this.get_label("acl.nouser"))},rcube_webmail.prototype.acl_cancel=function(){this.ksearch_blur(),this.acl_popup.dialog("close")},rcube_webmail.prototype.acl_update=function(e){e.old?this.acl_remove_row(e.old):this.env.acl[e.id]&&this.acl_remove_row(e.id),this.acl_add_row(e,!0),this.ksearch_blur(),this.acl_popup.dialog("close")},rcube_webmail.prototype.acl_mode_switch=function(e){this.env.acl_advanced=!this.env.acl_advanced,this.enable_command("acl-delete","acl-edit",!1),this.http_request("settings/plugin.acl","_act=list&_mode="+(this.env.acl_advanced?"advanced":"simple")+"&_mbox="+urlencode(this.env.mailbox),this.set_busy(!0,"loading"))},rcube_webmail.prototype.acl_list_init=function(){var e=this.env.acl_advanced?"addClass":"removeClass";$("#acl-switch")[e]("selected"),$(this.gui_objects.acltable)[e]("advanced"),this.acl_list=new rcube_list_widget(this.gui_objects.acltable,{multiselect:!0,draggable:!1,keyboard:!0}),this.acl_list.addEventListener("select",function(e){rcmail.acl_list_select(e)}).addEventListener("dblclick",function(e){rcmail.acl_list_dblclick(e)}).addEventListener("keypress",function(e){rcmail.acl_list_keypress(e)}).init()},rcube_webmail.prototype.acl_list_select=function(e){rcmail.enable_command("acl-delete",0<e.get_selection().length),rcmail.enable_command("acl-edit",1==e.get_selection().length),e.focus()},rcube_webmail.prototype.acl_list_dblclick=function(e){this.acl_edit()},rcube_webmail.prototype.acl_list_keypress=function(e){e.key_pressed==e.ENTER_KEY?this.command("acl-edit"):e.key_pressed!=e.DELETE_KEY&&e.key_pressed!=e.BACKSPACE_KEY||this.acl_form&&this.acl_form.is(":visible")||this.command("acl-delete")},rcube_webmail.prototype.acl_list_update=function(e){$(this.gui_objects.acltable).html(e),this.acl_list_init()},rcube_webmail.prototype.acl_get_usernames=function(){for(var e,t=[],a=this.acl_list,l=a.get_selection(),c=0,i=l.length;c<i;c++)this.env.acl_specials.length&&0<=$.inArray(l[c],this.env.acl_specials)?t.push(l[c]):(e=a.rows[l[c]])&&(e=$(e.obj).data("userid"))&&t.push(e);return t},rcube_webmail.prototype.acl_remove_row=function(e){var t=this.acl_list;t.remove_row(e),t.clear_selection(),$("#rcmrow"+e).remove(),this.env.acl[e]=null,this.enable_command("acl-delete",0<t.get_selection().length),this.enable_command("acl-edit",1==t.get_selection().length)},rcube_webmail.prototype.acl_add_row=function(l,e){var t,a,c=[],i=[],s=l.id,n=this.acl_list,o=this.env.acl_advanced?[]:this.env.acl_items,r=this.gui_objects.acltable,r=$("thead > tr",r).clone();for(t in $("th",r).map(function(){var e=$("<td>"),t=$(this).attr("title"),a=this.className.replace(/^acl/,"");t&&e.attr("title",t),"user"==(a=o&&o[a]?o[a]:a)?e.addClass(a).attr("title",l.title).append($("<a>").text(l.display)):e.addClass(this.className+" "+rcmail.acl_class(l.acl,a)).html("<span/>"),$(this).replaceWith(e)}),r=r.attr({id:"rcmrow"+s,"data-userid":l.username}).get(0),this.env.acl[s]=l.acl,this.env.acl)this.env.acl[t]&&(this.env.acl_specials.length&&0<=$.inArray(t,this.env.acl_specials)?i:c).push(t);for(c.sort(),t=0,a=(c=i.concat(c)).length;t<a&&c[t]!=s;t++);t&&t<a?($("#rcmrow"+c[t-1]).after(r),n.init_row(r),n.rowcount++):n.insert_row(r),e&&n.select_row(l.id)},rcube_webmail.prototype.acl_init_form=function(e){var t,a,l,c="",i="user",s=($("body"),$("#advancedrights")),n=$("#simplerights"),o=$("#acluser"),r=$("#usertype"),n=(this.acl_form||o.click(l=function(){$('input[value="user"]').prop("checked",!0)}).keypress(l),this.acl_form=$("#aclform"),l=this.env.acl_advanced?(s.show(),n.hide(),s):(n.show(),s.hide(),n),(s=$(":checkbox",l)).attr("checked",!1),e&&(t=this.acl_list.rows[e])?(t=t.obj,s.map(function(){(a=$("td."+this.id,t)).length&&a.hasClass("enabled")&&(this.checked=!0)}),!this.env.acl_specials.length||$.inArray(e,this.env.acl_specials)<0?c=$(t).data("userid"):i=e):s.filter(function(){return this.id.match(/^acl([lrs]|read)$/)}).prop("checked",!0),o.val(c),$("input[value="+i+"]").prop("checked",!0),this.acl_id=e,{}),_=this,d=document.body;n[this.get_label("save")]=function(e){_.command("acl-save")},n[this.get_label("cancel")]=function(e){_.command("acl-cancel")},this.acl_popup=this.show_popup_dialog(this.acl_form.show(),e?this.get_label("acl.editperms"):this.get_label("acl.newuser"),n,{button_classes:["mainaction submit","cancel"],modal:!0,closeOnEscape:!0,close:function(e,t){(_.is_framed()?parent.rcmail:_).ksearch_hide(),_.acl_form.appendTo(d).hide(),$(this).remove(),window.focus()}}),("user"==i?o:$("input:checked",r)).focus()},rcube_webmail.prototype.acl_class=function(e,t){var a,l,c=0;for(e=String(e),a=0,l=(t=String(t)).length;a<l;a++)-1<e.indexOf(t[a])&&c++;return c==l?"enabled":c?"partial":"disabled"};
