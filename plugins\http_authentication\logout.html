<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Logout</title>
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.6/jquery.min.js"></script>
<script type="text/javascript">

// as seen on http://stackoverflow.com/questions/31326/is-there-a-browser-equivalent-to-ies-clearauthenticationcache
$(document).ready(function(){
	if (document.all && document.execCommand) {
		document.execCommand("ClearAuthenticationCache", "false");
	 }
	 else {
		 $.ajax({
			url: location.href,
			type: 'POST',
			username: '__LOGOUT__',
			password: '***********'
		});
	}
});

</script>
</head>
<body>
<h1>You've successfully been logged out!</h1>

</body>