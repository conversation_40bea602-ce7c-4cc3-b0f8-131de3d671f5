/* Afrikaans initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON>. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.af = {
	closeText: "Selekteer",
	prevText: "Vorige",
	nextText: "Volgende",
	currentText: "Vandag",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON>", "<PERSON>", "September", "Okto<PERSON>", "November", "Desember" ],
	monthNamesShort: [ "Jan", "Feb", "Mrt", "Apr", "<PERSON>", "Jun",
	"Jul", "Aug", "Sep", "Okt", "Nov", "<PERSON>" ],
	dayNames: [ "Sondag", "<PERSON>andag", "<PERSON>sdag", "Woensdag", "Donderdag", "<PERSON>rydag", "Saterdag" ],
	dayNamesShort: [ "<PERSON>", "Maa", "Din", "Woe", "Don", "Vry", "Sat" ],
	dayNamesMin: [ "So", "Ma", "Di", "Wo", "Do", "Vr", "Sa" ],
	weekHeader: "Wk",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.af );

return datepicker.regional.af;

} );
