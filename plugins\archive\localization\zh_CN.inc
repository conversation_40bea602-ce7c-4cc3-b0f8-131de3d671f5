<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = '存档';
$labels['buttontitle'] = '存档该信息';
$labels['archived'] = '存档成功';
$labels['archivedreload'] = '存档成功。请刷新本页以查看新的存档文件夹。';
$labels['archiveerror'] = '部分信息无法存档';
$labels['archivefolder'] = '存档';
$labels['settingstitle'] = '存档';
$labels['archivetype'] = '分类存档按';
$labels['archivetypeyear'] = '年(例如 存档/2012)';
$labels['archivetypemonth'] = '月(例如 存档/2012/06)';
$labels['archivetypetbmonth'] = '月份 - Thunderbird 兼容格式 (比如 Archive/2012/2012-06)';
$labels['archivetypefolder'] = '原始文件夹';
$labels['archivetypefolderyear'] = '年份与原始文件夹（例如：Archive/2012/...）';
$labels['archivetypefoldermonth'] = '年份、月份与原始文件夹（例如：Archive/2012/06/...）';
$labels['archivetypesender'] = '发件人邮件';
$labels['unkownsender'] = '未知';
$labels['readonarchive'] = '在归档中标记信息为已读';
