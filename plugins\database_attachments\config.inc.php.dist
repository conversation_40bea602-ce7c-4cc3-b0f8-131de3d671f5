<?php

// By default this plugin stores attachments in filesystem
// and copies them into sql database.
// You can change it to use 'memcache', 'memcached', 'redis' or 'apc'.
// -----------------------------------------------------------
// WARNING: Remember to set max_allowed_packet in database or
//          config to match with expected max attachment size.
// -----------------------------------------------------------
$config['database_attachments_cache'] = 'db';

// Attachment data expires after specified TTL time in seconds (max.2592000).
// Default is 12 hours.
$config['database_attachments_cache_ttl'] = 12 * 60 * 60;
