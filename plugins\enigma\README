Enigma Plugin for Roundcube

This plugin adds support for viewing and sending of signed and encrypted
messages in PGP (RFC 2440) and PGP/MIME (RFC 3156) format.

The plugin uses gpg binary on the server and stores all keys
(including private keys of the users) on the server.
Encryption/decryption is done server-side. So, this plugin
is for users that trust the server.

For multi-host environments see enigma_multihost setting description.

Implemented features:
+ PGP: signatures verification
+ PGP: messages decryption
+ PGP: Sending of encrypted/signed messages
+ PGP: keys management UI (key import, export, delete)
+ PGP: key generation (client- or server-side)
+ Handling of PGP keys attached to incoming messages
+ User preferences to disable plugin features
+ Attaching public keys to email
+ Key server(s) support (search, import)


INSTALLATION
------------

1. Rename config.inc.php.dist to config.inc.php.
2. Create a directory for keys storage that is writeable for the PHP process.
   This directory should be out of the document root, so it is not accessible
   from the web browser. Set it's location in $config['enigma_pgp_homedir'].
3. Make sure GnuPG is installed.


TODO
----

- Handling of big messages with temp files (? - security)
- Key info in contact details page (optional)
- Extended key management:
   - disable,
   - revoke,
   - change expiration date, change passphrase, add photo,
   - manage user IDs
   - export private keys
- Generate revocation certs
- Search filter to see invalid/expired keys
- Key server(s) support (upload, refresh)
- Mark keys as trusted/untrusted, display appropriate message in verify/decrypt status
- Performance improvements:
   - cache decrypted message key id so we can skip decryption if we have no password in session
   - cache (last or successful only?) sig verification status to not verify on every msg preview (optional)
- S/MIME: Certs generation (?)
- S/MIME: Certs management
- S/MIME: signed messages verification
- S/MIME: encrypted messages decryption
- S/MIME: Sending signed/encrypted messages
- S/MIME: Handling of certs attached to incoming messages
- S/MIME: Certificate info in Contacts details page (optional)


KNOWN ISSUES
------------

There are some known issues with accepting key passphrases on various
system configurations. This is caused by issues in PinEntry handling.
Make sure that vendor/bin/crypt-gpg-pinentry works from command line.

Possible reasons:
- non-working loader in shebang (#! /usr/bin/env php)
  Make sure it works for the user the php scripts are executed upon
  (i.e. apache, www-data, etc.)
- SELinux setting, try command: setsebool -P httpd_unified 0

Note: pinentry is used with gpg >= 2.0 and <= 2.1.12.
Note: for server use GnuPG developers still recommend version 1.4.
