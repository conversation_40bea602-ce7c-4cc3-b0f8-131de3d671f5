/**
 * Archive plugin script
 *
 * @licstart  The following is the entire license notice for the
 * JavaScript code in this file.
 *
 * Copyright (c) The Roundcube Dev Team
 *
 * The JavaScript code in this page is free software: you can redistribute it
 * and/or modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version.
 *
 * @licend  The above is the entire license notice
 * for the JavaScript code in this file.
 */
function rcmail_archive(i){var e;rcmail_is_archive()||(e=rcmail.selection_post_data())._uid&&(rcmail.enable_command(rcmail.env.message_commands,!1),rcmail.enable_command("plugin.archive",!1),rcmail.with_selected_messages("move",e,null,"plugin.move2archive"),rcmail.show_contentframe(!1))}function rcmail_is_archive(){return rcmail.env.mailbox==rcmail.env.archive_folder||rcmail.env.mailbox.startsWith(rcmail.env.archive_folder+rcmail.env.delimiter)}window.rcmail&&rcmail.addEventListener("init",function(i){var e;rcmail.register_command("plugin.archive",rcmail_archive,rcmail.env.uid&&!rcmail_is_archive()),rcmail.message_list&&rcmail.message_list.addEventListener("select",function(i){rcmail.enable_command("plugin.archive",0<i.get_selection().length&&!rcmail_is_archive())}),rcmail.env.archive_folder&&((e=rcmail.subscription_list?rcmail.subscription_list.get_item(rcmail.env.archive_folder):rcmail.get_folder_li(rcmail.env.archive_folder,"",!0))&&$(e).addClass("archive"),rcmail.addEventListener("menu-open",function(i){var e;"folder-selector"==i.name&&(e=rcmail.env.archive_folder,$("a",i.obj).filter(function(){return $(this).data("id")==e}).parent().addClass("archive"))}))});
