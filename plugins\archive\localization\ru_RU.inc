<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = 'Архив';
$labels['buttontitle'] = 'Переместить выбранное в архив';
$labels['archived'] = 'Перенесено в Архив';
$labels['archivedreload'] = 'Успешно заархивировано. Обновите страницу, чтобы увидеть новые папки архива.';
$labels['archiveerror'] = 'Некоторые сообщения не могут быть заархивированы';
$labels['archivefolder'] = 'Архив';
$labels['settingstitle'] = 'Архив';
$labels['archivetype'] = 'Разделить архив по';
$labels['archivetypeyear'] = 'Год (например, Архив/2012)';
$labels['archivetypemonth'] = 'Месяц (например, Архив/2012/06)';
$labels['archivetypetbmonth'] = 'Месяц - совместимо с Thunderbird (например, Архив/2012/2012-06)';
$labels['archivetypefolder'] = 'Исходная папка';
$labels['archivetypefolderyear'] = 'Год и исходная папка (например, Архив/2012/...)';
$labels['archivetypefoldermonth'] = 'Год, месяц и исходная папка (например, Архив/2012/06/...)';
$labels['archivetypesender'] = 'Адрес отправителя';
$labels['unkownsender'] = 'неизвестно';
$labels['readonarchive'] = 'Помечать как прочитанное при архивировании';
