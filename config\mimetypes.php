<?php

/**
 * Local mapping file to specify mime-types based on common file-name extensions
 *
 * Please note that this mapping takes precedence over the content-based mime-type detection
 * and should only contain mappings which cannot be detected properly from the file contents.
 */

return [
  'xls' => 'application/vnd.ms-excel',
  'xlm' => 'application/vnd.ms-excel',
  'xla' => 'application/vnd.ms-excel',
  'xlc' => 'application/vnd.ms-excel',
  'xlt' => 'application/vnd.ms-excel',
  'xlw' => 'application/vnd.ms-excel',
  'pdf' => 'application/pdf',
  'ppt' => 'application/vnd.ms-powerpoint',
  'pps' => 'application/vnd.ms-powerpoint',
  'pot' => 'application/vnd.ms-powerpoint',
  'doc' => 'application/msword',
  'dot' => 'application/msword',
  'odc' => 'application/vnd.oasis.opendocument.chart',
  'otc' => 'application/vnd.oasis.opendocument.chart-template',
  'odf' => 'application/vnd.oasis.opendocument.formula',
  'otf' => 'application/vnd.oasis.opendocument.formula-template',
  'odg' => 'application/vnd.oasis.opendocument.graphics',
  'otg' => 'application/vnd.oasis.opendocument.graphics-template',
  'odi' => 'application/vnd.oasis.opendocument.image',
  'oti' => 'application/vnd.oasis.opendocument.image-template',
  'odp' => 'application/vnd.oasis.opendocument.presentation',
  'otp' => 'application/vnd.oasis.opendocument.presentation-template',
  'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
  'ots' => 'application/vnd.oasis.opendocument.spreadsheet-template',
  'odt' => 'application/vnd.oasis.opendocument.text',
  'otm' => 'application/vnd.oasis.opendocument.text-master',
  'ott' => 'application/vnd.oasis.opendocument.text-template',
  'oth' => 'application/vnd.oasis.opendocument.text-web',
  'docm' => 'application/vnd.ms-word.document.macroEnabled.12',
  'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'dotm' => 'application/vnd.ms-word.template.macroEnabled.12',
  'dotx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  'ppsm' => 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
  'ppsx' => 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
  'pptm' => 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
  'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'xlsb' => 'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
  'xlsm' => 'application/vnd.ms-excel.sheet.macroEnabled.12',
  'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'xps' => 'application/vnd.ms-xpsdocument',
  'rar' => 'application/x-rar-compressed',
  '7z'  => 'application/x-7z-compressed',
  's7z' => 'application/x-7z-compressed',
  'vcf' => 'text/vcard',
  'ics' => 'text/calendar',
];
