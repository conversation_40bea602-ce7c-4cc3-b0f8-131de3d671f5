<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = 'Archivio';
$labels['buttontitle'] = 'Archivia questo messaggio';
$labels['archived'] = 'Archiviato correttamente';
$labels['archivedreload'] = 'Archiviata con successo. Ricarica la pagina per visualizzare le nuove cartelle.';
$labels['archiveerror'] = 'Alcuni messaggi non possono essere archiviati';
$labels['archivefolder'] = 'Archivio';
$labels['settingstitle'] = 'Archivio';
$labels['archivetype'] = 'Dividi archivio per';
$labels['archivetypeyear'] = 'Anno (es. Archivio/2012)';
$labels['archivetypemonth'] = 'Mese (es. Archivio/2012/06)';
$labels['archivetypetbmonth'] = 'Mese - compatibile con Thunderbird (es Archivio/2012/2012-06)';
$labels['archivetypefolder'] = 'Cartella di origine';
$labels['archivetypefolderyear'] = 'Anno e la cartella originale (p.e. Archivio/2012/...)';
$labels['archivetypefoldermonth'] = 'Anno, mese e la cartella originale (p.e. Archivio/2012/06/...)';
$labels['archivetypesender'] = 'Mittente email';
$labels['unkownsender'] = 'sconosciuto';
$labels['readonarchive'] = 'Segna i messaggi in archivio come letti';
