<?php

// ---------------------------------------------------------------------
// WARNING: Do not edit this file! Copy configuration to config.inc.php.
// ---------------------------------------------------------------------

/*
 +-----------------------------------------------------------------------+
 | Default settings for all configuration options                        |
 |                                                                       |
 | This file is part of the Roundcube Webmail client                     |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+
*/

$config = [];

// ----------------------------------
// SQL DATABASE
// ----------------------------------

// Database connection string (DSN) for read+write operations
// Format (compatible with PEAR MDB2): db_provider://user:password@host/database
// Currently supported db_providers: mysql, pgsql, sqlite, mssql, sqlsrv, oracle
// For examples see https://pear.php.net/manual/en/package.database.mdb2.intro-dsn.php
// Note: for SQLite use absolute path (Linux): 'sqlite:////full/path/to/sqlite.db?mode=0646'
//       or (Windows): 'sqlite:///C:/full/path/to/sqlite.db'
// Note: Various drivers support various additional arguments for connection,
//       for Mysql: key, cipher, cert, capath, ca, verify_server_cert,
//       for Postgres: application_name, sslmode, sslcert, sslkey, sslrootcert, sslcrl, sslcompression, service.
//       e.g. 'mysql://roundcube:@localhost/roundcubemail?verify_server_cert=false'
$config['db_dsnw'] = 'mysql://roundcube:@localhost/roundcubemail';

// Database DSN for read-only operations (if empty write database will be used)
// useful for database replication
$config['db_dsnr'] = '';

// Disable the use of already established dsnw connections for subsequent reads
$config['db_dsnw_noread'] = false;

// use persistent db-connections
// beware this will not "always" work as expected
// see: https://www.php.net/manual/en/features.persistent-connections.php
$config['db_persistent'] = false;

// you can define specific table (and sequence) names prefix
$config['db_prefix'] = '';

// Mapping of table names and connections to use for ALL operations.
// This can be used in a setup with replicated databases and a DB master
// where read/write access to cache tables should not go to master.
$config['db_table_dsn'] = [
//    'cache' => 'r',
//    'cache_index' => 'r',
//    'cache_thread' => 'r',
//    'cache_messages' => 'r',
];

// It is possible to specify database variable values e.g. some limits here.
// Use them if your server is not MySQL or for better performance.
// For example Roundcube uses max_allowed_packet value (in bytes)
// which limits query size for database cache operations.
$config['db_max_allowed_packet'] = null;


// ----------------------------------
// LOGGING/DEBUGGING
// ----------------------------------

// log driver:  'syslog', 'stdout' or 'file'.
$config['log_driver'] = 'file';

// date format for log entries
// (read https://php.net/manual/en/function.date.php for all format characters)
$config['log_date_format'] = 'd-M-Y H:i:s O';

// length of the session ID to prepend each log line with
// set to 0 to avoid session IDs being logged.
$config['log_session_id'] = 8;

// Default extension used for log file name
$config['log_file_ext'] = '.log';

// Syslog ident string to use, if using the 'syslog' log driver.
$config['syslog_id'] = 'roundcube';

// Syslog facility to use, if using the 'syslog' log driver.
// For possible values see installer or https://php.net/manual/en/function.openlog.php
$config['syslog_facility'] = LOG_USER;

// Activate this option if logs should be written to per-user directories.
// Data will only be logged if a directory <log_dir>/<username>/ exists and is writable.
$config['per_user_logging'] = false;

// Log sent messages to <log_dir>/sendmail.log or to syslog
$config['smtp_log'] = true;

// Log successful/failed logins to <log_dir>/userlogins.log or to syslog
$config['log_logins'] = false;

// Log session debug information/authentication errors to <log_dir>/session.log or to syslog
$config['session_debug'] = false;

// Log SQL queries to <log_dir>/sql.log or to syslog
$config['sql_debug'] = false;

// Log IMAP conversation to <log_dir>/imap.log or to syslog
$config['imap_debug'] = false;

// Log LDAP conversation to <log_dir>/ldap.log or to syslog
$config['ldap_debug'] = false;

// Log SMTP conversation to <log_dir>/smtp.log or to syslog
$config['smtp_debug'] = false;

// Log Memcache conversation to <log_dir>/memcache.log or to syslog
$config['memcache_debug'] = false;

// Log APC conversation to <log_dir>/apc.log or to syslog
$config['apc_debug'] = false;

// Log Redis conversation to <log_dir>/redis.log or to syslog
$config['redis_debug'] = false;


// ----------------------------------
// IMAP
// ----------------------------------

// The IMAP host (and optionally port number) chosen to perform the log-in.
// Leave blank to show a textbox at login, give a list of hosts
// to display a pulldown menu or set one host as string.
// Enter hostname with prefix ssl:// to use Implicit TLS, or use
// prefix tls:// to use STARTTLS.
// If port number is omitted it will be set to 993 (for ssl://) or 143 otherwise.
// Supported replacement variables:
// %n - hostname ($_SERVER['SERVER_NAME'])
// %t - hostname without the first part
// %d - domain (http hostname $_SERVER['HTTP_HOST'] without the first part)
// %s - domain name after the '@' from e-mail address provided at login screen
// For example %n = mail.domain.tld, %t = domain.tld
// WARNING: After hostname change update of mail_host column in users table is
//          required to match old user data records with the new host.
$config['imap_host'] = 'localhost:143';

// IMAP authentication method (DIGEST-MD5, CRAM-MD5, LOGIN, PLAIN or null).
// Use 'IMAP' to authenticate with IMAP LOGIN command.
// By default the most secure method (from supported) will be selected.
$config['imap_auth_type'] = null;

// IMAP socket context options
// See https://php.net/manual/en/context.ssl.php
// The example below enables server certificate validation
//$config['imap_conn_options'] = [
//  'ssl'         => [
//     'verify_peer'  => true,
//     'verify_depth' => 3,
//     'cafile'       => '/etc/openssl/certs/ca.crt',
//   ],
// ];
// Note: These can be also specified as an array of options indexed by hostname
$config['imap_conn_options'] = null;

// IMAP connection timeout, in seconds. Default: 0 (use default_socket_timeout)
$config['imap_timeout'] = 0;

// Optional IMAP authentication identifier to be used as authorization proxy
$config['imap_auth_cid'] = null;

// Optional IMAP authentication password to be used for imap_auth_cid
$config['imap_auth_pw'] = null;

// If you know your imap's folder delimiter, you can specify it here.
// Otherwise it will be determined automatically
$config['imap_delimiter'] = null;

// If you know your imap's folder vendor, you can specify it here.
// Otherwise it will be determined automatically. Use lower-case
// identifiers, e.g. 'dovecot', 'cyrus', 'gimap', 'hmail', 'uw-imap'.
$config['imap_vendor'] = null;

// If IMAP server doesn't support NAMESPACE extension, but you're
// using shared folders or personal root folder is non-empty, you'll need to
// set these options. All can be strings or arrays of strings.
// Note: Folders need to be ended with directory separator, e.g. "INBOX."
//       (special directory "~" is an exception to this rule)
// Note: These can be used also to overwrite server's namespaces
// Note: Set these to FALSE to disable access to specified namespace
$config['imap_ns_personal'] = null;
$config['imap_ns_other']    = null;
$config['imap_ns_shared']   = null;

// By default IMAP capabilities are read after connection to IMAP server
// In some cases, e.g. when using IMAP proxy, there's a need to refresh the list
// after login. Set to True if you've got this case.
$config['imap_force_caps'] = false;

// By default list of subscribed folders is determined using LIST-EXTENDED
// extension if available. Some servers (dovecot 1.x) returns wrong results
// for shared namespaces in this case. https://github.com/roundcube/roundcubemail/issues/2474
// Enable this option to force LSUB command usage instead.
// Deprecated: Use imap_disabled_caps = ['LIST-EXTENDED']
$config['imap_force_lsub'] = false;

// Some server configurations (e.g. Courier) doesn't list folders in all namespaces
// Enable this option to force listing of folders in all namespaces
$config['imap_force_ns'] = false;

// Some servers return hidden folders (name starting with a dot)
// from user home directory. IMAP RFC does not forbid that.
// Enable this option to hide them and disable possibility to create such.
$config['imap_skip_hidden_folders'] = false;

// Some servers do not support folders with both folders and messages inside
// If your server supports that use true, if it does not, use false.
// By default it will be determined automatically (once per user session).
$config['imap_dual_use_folders'] = null;

// List of disabled imap extensions.
// Use if your IMAP server has broken implementation of some feature
// and you can't remove it from CAPABILITY string on server-side.
// For example UW-IMAP server has broken ESEARCH.
// Note: Because the list is cached, re-login is required after change.
$config['imap_disabled_caps'] = [];

// Log IMAP session identifiers after each IMAP login.
// This is used to relate IMAP session with Roundcube user sessions
$config['imap_log_session'] = false;

// Type of IMAP indexes cache. Supported values: 'db', 'apc' and 'memcache' or 'memcached'.
$config['imap_cache'] = null;

// Enables messages cache. Only 'db' cache is supported.
// This requires an IMAP server that supports QRESYNC and CONDSTORE
// extensions (RFC7162). See synchronize() in program/lib/Roundcube/rcube_imap_cache.php
// for further info, or if you experience syncing problems.
$config['messages_cache'] = false;

// Lifetime of IMAP indexes cache. Possible units: s, m, h, d, w
$config['imap_cache_ttl'] = '10d';

// Lifetime of messages cache. Possible units: s, m, h, d, w
$config['messages_cache_ttl'] = '10d';

// Maximum cached message size in kilobytes.
// Note: On MySQL this should be less than (max_allowed_packet - 30%)
$config['messages_cache_threshold'] = 50;


// ----------------------------------
// SMTP
// ----------------------------------

// SMTP server host (and optional port number) for sending mails.
// Enter hostname with prefix ssl:// to use Implicit TLS, or use
// prefix tls:// to use STARTTLS.
// If port number is omitted it will be set to 465 (for ssl://) or 587 otherwise.
// Supported replacement variables:
// %h - user's IMAP hostname
// %n - hostname ($_SERVER['SERVER_NAME'])
// %t - hostname without the first part
// %d - domain (http hostname $_SERVER['HTTP_HOST'] without the first part)
// %z - IMAP domain (IMAP hostname without the first part)
// For example %n = mail.domain.tld, %t = domain.tld
// To specify different SMTP servers for different IMAP hosts provide an array
// of IMAP host (no prefix or port) and SMTP server e.g. ['imap.example.com' => 'smtp.example.net']
$config['smtp_host'] = 'localhost:587';

// SMTP username (if required)
// Note: %u variable will be replaced with current user's username
// Note: To disable user authentication set smtp_user and smtp_pass to ''.
$config['smtp_user'] = '%u';

// SMTP password (if required)
// Note: When set to '%p' current user's password will be used
// Note: To disable user authentication set smtp_user and smtp_pass to ''.
$config['smtp_pass'] = '%p';

// SMTP AUTH type (DIGEST-MD5, CRAM-MD5, LOGIN, PLAIN or empty to use
// best server supported one)
$config['smtp_auth_type'] = null;

// Optional SMTP authentication identifier to be used as authorization proxy
$config['smtp_auth_cid'] = null;

// Optional SMTP authentication password to be used for smtp_auth_cid
$config['smtp_auth_pw'] = null;

// Pass the username (XCLIENT LOGIN) to the server
$config['smtp_xclient_login'] = false;

// Pass the remote IP (XCLIENT ADDR) to the server
$config['smtp_xclient_addr'] = false;


// SMTP HELO host
// Hostname to give to the remote server for SMTP 'HELO' or 'EHLO' messages
// Leave this blank and you will get the server variable 'server_name' or
// localhost if that isn't defined.
$config['smtp_helo_host'] = '';

// SMTP connection timeout, in seconds. Default: 0 (use default_socket_timeout)
// Note: There's a known issue where using ssl connection with
// timeout > 0 causes connection errors (https://bugs.php.net/bug.php?id=54511)
$config['smtp_timeout'] = 0;

// SMTP socket context options
// See https://php.net/manual/en/context.ssl.php
// The example below enables server certificate validation, and
// requires 'smtp_timeout' to be non zero.
// $config['smtp_conn_options'] = [
//     'ssl' => [
//         'verify_peer'  => true,
//         'verify_depth' => 3,
//         'cafile'       => '/etc/openssl/certs/ca.crt',
//     ],
// ];
// Note: These can be also specified as an array of options indexed by hostname
$config['smtp_conn_options'] = null;


// ----------------------------------
// OAuth
// ----------------------------------

// Enable OAuth2 by defining a provider. Use 'generic' here
$config['oauth_provider'] = null;

// Provider name to be displayed on the login button
$config['oauth_provider_name'] = 'Google';

// Mandatory: OAuth client ID for your Roundcube installation
$config['oauth_client_id'] = null;

// Mandatory: OAuth client secret
$config['oauth_client_secret'] = null;

// Mandatory: URI for OAuth user authentication (redirect)
$config['oauth_auth_uri'] = null;

// Mandatory: Endpoint for OAuth authentication requests (server-to-server)
$config['oauth_token_uri'] = null;

// Optional: Endpoint to query user identity if not provided in auth response
$config['oauth_identity_uri'] = null;

// Optional: disable SSL certificate check on HTTP requests to OAuth server
// See https://docs.guzzlephp.org/en/stable/request-options.html#verify for possible values
$config['oauth_verify_peer'] = true;

// Mandatory: OAuth scopes to request (space-separated string)
$config['oauth_scope'] = null;

// Optional: additional query parameters to send with login request (hash array)
$config['oauth_auth_parameters'] = [];

// Optional: array of field names used to resolve the username within the identity information
$config['oauth_identity_fields'] = null;

// Boolean: automatically redirect to OAuth login when opening Roundcube without a valid session
$config['oauth_login_redirect'] = false;

// Optional: For backends that don't support XOAUTH2/OAUTHBEARER method we can still use
// OpenIDC protocol to get a short-living password (claim) for the user to log into IMAP/SMTP.
// That password have to have (at least) the same expiration time as the token, and will be
// renewed on token refresh.
// Note: The claim have to be added to 'oauth_scope' above.
$config['oauth_password_claim'] = null;

///// Example config for Gmail

// Register your service at https://console.developers.google.com/
// - use https://<your-roundcube-url>/index.php/login/oauth as redirect URL

// $config['imap_host'] = 'ssl://imap.gmail.com';
// $config['oauth_provider'] = 'google';
// $config['oauth_provider_name'] = 'Google';
// $config['oauth_client_id'] = "<your-credentials-client-id>";
// $config['oauth_client_secret'] = "<your-credentials-client-secret>";
// $config['oauth_auth_uri'] = "https://accounts.google.com/o/oauth2/auth";
// $config['oauth_token_uri'] = "https://oauth2.googleapis.com/token";
// $config['oauth_identity_uri'] = 'https://www.googleapis.com/oauth2/v1/userinfo';
// $config['oauth_scope'] = "email profile openid https://mail.google.com/";
// $config['oauth_auth_parameters'] = ['access_type' => 'offline', 'prompt' => 'consent'];

///// Example config for Outlook.com (Office 365)

// Register your OAuth client at https://portal.azure.com
// - use https://<your-roundcube-url>/index.php/login/oauth as redirect URL
// - grant permissions to Microsoft Graph API "IMAP.AccessAsUser.All", "SMTP.Send", "User.Read" and "offline_access"

// $config['imap_host'] = 'ssl://outlook.office365.com';
// $config['smtp_host'] = 'ssl://smtp.office365.com';

// $config['oauth_provider'] = 'outlook';
// $config['oauth_provider_name'] = 'Outlook.com';
// $config['oauth_client_id'] = "<your-credentials-client-id>";
// $config['oauth_client_secret'] = "<your-credentials-client-secret>";
// $config['oauth_auth_uri'] = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
// $config['oauth_token_uri'] = "https://login.microsoftonline.com/common/oauth2/v2.0/token";
// $config['oauth_identity_uri'] = "https://graph.microsoft.com/v1.0/me";
// $config['oauth_identity_fields'] = ['email', 'userPrincipalName'];
// $config['oauth_scope'] = "https://outlook.office365.com/IMAP.AccessAsUser.All https://outlook.office365.com/SMTP.Send User.Read offline_access";
// $config['oauth_auth_parameters'] = ['nonce' => mt_rand()];

// ----------------------------------
// LDAP
// ----------------------------------

// Type of LDAP cache. Supported values: 'db', 'apc' and 'memcache' or 'memcached'.
$config['ldap_cache'] = 'db';

// Lifetime of LDAP cache. Possible units: s, m, h, d, w
$config['ldap_cache_ttl'] = '10m';


// ----------------------------------
// CACHE(S)
// ----------------------------------

// Use these hosts for accessing memcached
// Define any number of hosts in the form of hostname:port or unix:///path/to/socket.file
// Example: ['localhost:11211', '***********2:11211', 'unix:///var/tmp/memcached.sock'];
$config['memcache_hosts'] = null;

// Controls the use of a persistent connections to memcache servers
// See https://php.net/manual/en/memcache.addserver.php
$config['memcache_pconnect'] = true;

// Value in seconds which will be used for connecting to the daemon
// See https://php.net/manual/en/memcache.addserver.php
$config['memcache_timeout'] = 1;

// Controls how often a failed server will be retried (value in seconds).
// Setting this parameter to -1 disables automatic retry.
// See https://php.net/manual/en/memcache.addserver.php
$config['memcache_retry_interval'] = 15;

// Use these hosts for accessing Redis.
// Currently only one host is supported. Cluster support may come in a future release.
// You can pass 4 fields, host, port (optional), database (optional) and password (optional).
// Unset fields will be set to the default values host=127.0.0.1, port=6379.
// Examples:
//     ['localhost:6379'];
//     ['***********:6379:1:secret'];
//     ['unix:///var/run/redis/redis-server.sock:1:secret'];
$config['redis_hosts'] = null;

// Maximum size of an object in memcache (in bytes). Default: 2MB
$config['memcache_max_allowed_packet'] = '2M';

// Maximum size of an object in APC cache (in bytes). Default: 2MB
$config['apc_max_allowed_packet'] = '2M';

// Maximum size of an object in Redis cache (in bytes). Default: 2MB
$config['redis_max_allowed_packet'] = '2M';


// ----------------------------------
// SYSTEM
// ----------------------------------

// THIS OPTION WILL ALLOW THE INSTALLER TO RUN AND CAN EXPOSE SENSITIVE CONFIG DATA.
// ONLY ENABLE IT IF YOU'RE REALLY SURE WHAT YOU'RE DOING!
$config['enable_installer'] = false;

// don't allow these settings to be overridden by the user
$config['dont_override'] = [];

// List of disabled UI elements/actions
$config['disabled_actions'] = [];

// define which settings should be listed under the 'advanced' block
// which is hidden by default
$config['advanced_prefs'] = [];

// provide an URL where a user can get support for this Roundcube installation
// PLEASE DO NOT LINK TO THE ROUNDCUBE.NET WEBSITE HERE!
$config['support_url'] = '';

// Location of the blank (watermark) frame page. By default it is the watermark.html
// file from the currently selected skin. Prepend name/path with a slash to use
// current skin folder. Remove the slash to point to a file in the Roundcube
// root directory. It can be also a full URL.
$config['blankpage_url'] = '/watermark.html';

// Logo image replacement. Specifies location of the image as:
// - URL relative to the document root of this Roundcube installation
// - full URL with http:// or https:// prefix
// - URL relative to the current skin folder (when starts with a '/')
//
// An array can be used to specify different logos for specific template files
// The array key specifies the place(s) the logo should be applied to and
// is made up of (up to) 3 parts:
// - skin name prefix (always with colon, can be replaced with *)
// - template name (or * for all templates)
// - logo type - it is used for logos used on multiple templates and the available types include:
//      '[favicon]' for favicon
//      '[print]' for logo on all print templates (e.g. messageprint, contactprint)
//      '[small]' for small screen logo in supported skins
//      '[dark]' and '[small-dark]' for dark mode logo in supported skins
//      '[link]' for adding a URL link to the logo image
//
// Example config for skin_logo
/*
   [
     // show the image /images/logo_login_small.png for the Login screen in the Elastic skin on small screens
     "elastic:login[small]" => "/images/logo_login_small.png",
     // show the image /images/logo_login.png for the Login screen in the Elastic skin
     "elastic:login" => "/images/logo_login.png",
     // add a link to the logo on the Login screen in the Elastic skin
     "elastic:login[link]" => "https://www.example.com",
     // add a link to the logo on all screens in the Elastic skin
     "elastic:*[link]" => "https://www.example.com",
     // add a link to the logo on all screens for all skins
     "[link]" => "https://www.example.com",
     // show the image /images/logo_small.png in the Elastic skin
     "elastic:*[small]" => "/images/logo_small.png",
     // show the image /images/larry.png in the Larry skin
     "larry:*" => "/images/larry.png",
     // show the image /images/logo_login.png on the login template in all skins
     "login" => "/images/logo_login.png",
     // show the image /images/logo_print.png for all print type logos in all skins
     "[print]" => "/images/logo_print.png",
   ];
*/
$config['skin_logo'] = null;

// Automatically register user in Roundcube database on successful (IMAP) logon.
// Set to false if only registered users should be allowed to the webmail.
// Note: If disabled you have to create records in Roundcube users table by yourself.
// Note: Roundcube does not manage/create users on a mail server.
$config['auto_create_user'] = true;

// Enables possibility to log in using email address from user identities
$config['user_aliases'] = false;

// use this folder to store log files
// must be writeable for the user who runs PHP process (Apache user if mod_php is being used)
// This is used by the 'file' log driver.
$config['log_dir'] = RCUBE_INSTALL_PATH . 'logs/';

// Location of temporary saved files such as attachments and cache files
// must be writeable for the user who runs PHP process (Apache user if mod_php is being used)
$config['temp_dir'] = RCUBE_INSTALL_PATH . 'temp/';

// expire files in temp_dir after 48 hours
// possible units: s, m, h, d, w
$config['temp_dir_ttl'] = '48h';

// Enforce connections over https
// With this option enabled, all non-secure connections will be redirected.
// It can be also a port number, hostname or hostname:port if they are
// different than default HTTP_HOST:443
$config['force_https'] = false;

// tell PHP that it should work as under secure connection
// even if it doesn't recognize it as secure ($_SERVER['HTTPS'] is not set)
// e.g. when you're running Roundcube behind a https proxy
// this option is mutually exclusive to 'force_https' and only either one of them should be set to true.
$config['use_https'] = false;

// Allow browser-autocompletion on login form.
// 0 - disabled, 1 - username and host only, 2 - username, host, password
$config['login_autocomplete'] = 0;

// Forces conversion of logins to lower case.
// 0 - disabled, 1 - only domain part, 2 - domain and local part.
// If users authentication is case-insensitive this must be enabled.
// Note: After enabling it all user records need to be updated, e.g. with query:
//       UPDATE users SET username = LOWER(username);
$config['login_lc'] = 2;

// Maximum length (in bytes) of logon username and password.
$config['login_username_maxlen'] = 1024;
$config['login_password_maxlen'] = 1024;

// Logon username filter. Regular expression for use with preg_match().
// Use special value 'email' if you accept only full email addresses as user logins.
// Example: '/^[a-z0-9_@.-]+$/'
$config['login_username_filter'] = null;

// Brute-force attacks prevention.
// The value specifies maximum number of failed logon attempts per minute.
$config['login_rate_limit'] = 3;

// Includes should be interpreted as PHP files
$config['skin_include_php'] = false;

// display product name and software version on login screen
// 0 - hide product name and version number, 1 - show product name only, 2 - show product name and version number
$config['display_product_info'] = 1;

// Session lifetime in minutes
$config['session_lifetime'] = 10;

// Session domain: .example.org
$config['session_domain'] = '';

// Session name. Default: 'roundcube_sessid'
$config['session_name'] = null;

// Session authentication cookie name. Default: 'roundcube_sessauth'
$config['session_auth_name'] = null;

// Session path. Defaults to PHP session.cookie_path setting.
$config['session_path'] = null;

// Session samesite. Defaults to PHP session.cookie_samesite setting.
// Requires PHP >= 7.3.0, see https://wiki.php.net/rfc/same-site-cookie for more info
// Possible values: null (default), 'Lax', or 'Strict'
$config['session_samesite'] = null;

// Backend to use for session storage. Can either be 'db' (default), 'redis', 'memcache', or 'php'
//
// If set to 'memcache' or 'memcached', a list of servers need to be specified in 'memcache_hosts'
// Make sure the Memcache extension (https://pecl.php.net/package/memcache) version >= 2.0.0
// or the Memcached extension (https://pecl.php.net/package/memcached) version >= 2.0.0 is installed.
//
// If set to 'redis', a server needs to be specified in 'redis_hosts'
// Make sure the Redis extension (https://pecl.php.net/package/redis) version >= 2.0.0 is installed.
//
// Setting this value to 'php' will use the default session save handler configured in PHP
$config['session_storage'] = 'db';

// List of trusted proxies
// X_FORWARDED_* and X_REAL_IP headers are only accepted from these IPs
$config['proxy_whitelist'] = [];

// List of trusted host names
// Attackers can modify Host header of the HTTP request causing $_SERVER['SERVER_NAME']
// or $_SERVER['HTTP_HOST'] variables pointing to a different host, that could be used
// to collect user names and passwords. Some server configurations prevent that, but not all.
// An empty list accepts any host name. The list can contain host names
// or PCRE patterns (without // delimiters, that will be added automatically).
$config['trusted_host_patterns'] = [];

// check client IP in session authorization
$config['ip_check'] = false;

// X-Frame-Options HTTP header value sent to prevent from Clickjacking.
// Possible values: sameorigin|deny|allow-from <uri>.
// Set to false in order to disable sending the header.
$config['x_frame_options'] = 'sameorigin';

// This key is used for encrypting purposes, like storing of imap password
// in the session. For historical reasons it's called DES_key, but it's used
// with any configured cipher_method (see below).
// For the default cipher_method a required key length is 24 characters.
$config['des_key'] = 'rcmail-!24ByteDESkey*Str';

// Encryption algorithm. You can use any method supported by OpenSSL.
// Default is set for backward compatibility to DES-EDE3-CBC,
// but you can choose e.g. AES-256-CBC which we consider a better choice.
$config['cipher_method'] = 'DES-EDE3-CBC';

// Automatically add this domain to user names for login
// Only for IMAP servers that require full e-mail addresses for login
// Specify an array with 'host' => 'domain' values to support multiple hosts
// Supported replacement variables:
// %h - user's IMAP hostname
// %n - hostname ($_SERVER['SERVER_NAME'])
// %t - hostname without the first part
// %d - domain (http hostname $_SERVER['HTTP_HOST'] without the first part)
// %z - IMAP domain (IMAP hostname without the first part)
// For example %n = mail.domain.tld, %t = domain.tld
$config['username_domain'] = '';

// Force domain configured in username_domain to be used for login.
// Any domain in username will be replaced by username_domain.
$config['username_domain_forced'] = false;

// This domain will be used to form e-mail addresses of new users
// Specify an array with 'host' => 'domain' values to support multiple hosts
// Supported replacement variables:
// %h - user's IMAP hostname
// %n - http hostname ($_SERVER['SERVER_NAME'])
// %d - domain (http hostname without the first part)
// %z - IMAP domain (IMAP hostname without the first part)
// For example %n = mail.domain.tld, %t = domain.tld
$config['mail_domain'] = '';

// Password character set, to change the password for user
// authentication or for password change operations
$config['password_charset'] = 'UTF-8';

// How many seconds must pass between emails sent by a user
$config['sendmail_delay'] = 0;

// Message size limit. Note that SMTP server(s) may use a different value.
// This limit is verified when user attaches files to a composed message.
// Size in bytes (possible unit suffix: K, M, G)
$config['max_message_size'] = '100M';

// Maximum number of recipients per message (including To, Cc, Bcc).
// Default: 0 (no limit)
$config['max_recipients'] = 0;

// Maximum number of recipients per message excluding Bcc header.
// This is a soft limit, which means we only display a warning to the user.
// Default: 5
$config['max_disclosed_recipients'] = 5;

// Maximum allowed number of members of an address group. Default: 0 (no limit)
// If 'max_recipients' is set this value should be less or equal
$config['max_group_members'] = 0;

// Name your service. This is displayed on the login screen and in the window title
$config['product_name'] = 'Roundcube Webmail';

// Add this user-agent to message headers when sending. Default: not set.
$config['useragent'] = null;

// try to load host-specific configuration
// see https://github.com/roundcube/roundcubemail/wiki/Configuration:-Multi-Domain-Setup
// for more details
$config['include_host_config'] = false;

// path to a text file which will be added to each sent message
// paths are relative to the Roundcube root folder
$config['generic_message_footer'] = '';

// path to a text file which will be added to each sent HTML message
// paths are relative to the Roundcube root folder
$config['generic_message_footer_html'] = '';

// add a received header to outgoing mails containing the creators IP and hostname
$config['http_received_header'] = false;

// Whether or not to encrypt the IP address and the host name
// these could, in some circles, be considered as sensitive information;
// however, for the administrator, these could be invaluable help
// when tracking down issues.
$config['http_received_header_encrypt'] = false;

// number of chars allowed for line when wrapping text.
// text wrapping is done when composing/sending messages
$config['line_length'] = 72;

// send plaintext messages as format=flowed
$config['send_format_flowed'] = true;

// According to RFC2298, return receipt envelope sender address must be empty.
// If this option is true, Roundcube will use user's identity as envelope sender for MDN responses.
$config['mdn_use_from'] = false;

// Set identities access level:
// 0 - many identities with possibility to edit all params
// 1 - many identities with possibility to edit all params but not email address
// 2 - one identity with possibility to edit all params
// 3 - one identity with possibility to edit all params but not email address
// 4 - one identity with possibility to edit only signature
$config['identities_level'] = 0;

// Maximum size of uploaded image (in kilobytes) for HTML identities.
// Images (in html signatures) are stored in database as data URIs.
$config['identity_image_size'] = 64;

// Maximum size of uploaded image (in kilobytes) for HTML responses.
// Images (in html responses) are stored in database as data URIs.
$config['response_image_size'] = 64;

// Mimetypes supported by the browser.
// Attachments of these types will open in a preview window.
// Either a comma-separated list or an array. Default list includes:
//     text/plain,text/html,
//     image/jpeg,image/gif,image/png,image/bmp,image/tiff,image/webp,
//     application/x-javascript,application/pdf,application/x-shockwave-flash
$config['client_mimetypes'] = null;

// Path to a local mime magic database file for PHPs finfo extension.
// Set to null if the default path should be used.
$config['mime_magic'] = null;

// Absolute path to a local mime.types mapping table file.
// This is used to derive mime-types from the filename extension or vice versa.
// Such a file is usually part of the apache webserver. If you don't find a file named mime.types on your system,
// download it from https://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types
$config['mime_types'] = null;

// path to imagemagick identify binary (if not set we'll use Imagick or GD extensions)
$config['im_identify_path'] = null;

// path to imagemagick convert binary (if not set we'll use Imagick or GD extensions)
$config['im_convert_path'] = null;

// Size of thumbnails from image attachments displayed below the message content.
// Note: whether images are displayed at all depends on the 'inline_images' option.
// Set to 0 to display images in full size.
$config['image_thumbnail_size'] = 240;

// maximum size of uploaded contact photos in pixel
$config['contact_photo_size'] = 160;

// Enable DNS checking for e-mail address validation
$config['email_dns_check'] = false;

// Disables saving sent messages in Sent folder (like gmail) (Default: false)
// Note: useful when SMTP server stores sent mail in user mailbox
$config['no_save_sent_messages'] = false;

// Improve system security by using special URL with security token.
// This can be set to a number defining token length. Default: 16.
// Warning: This requires http server configuration. Sample:
//    RewriteRule ^/roundcubemail/[a-zA-Z0-9]{16}/(.*) /roundcubemail/$1 [PT]
//    Alias /roundcubemail /var/www/roundcubemail/
// Warning: This feature does NOT work with request_path = 'SCRIPT_NAME'
// Note: Use assets_path to not prevent the browser from caching assets
$config['use_secure_urls'] = false;

// Specifies the full path of the original HTTP request, either as a real path or
// $_SERVER field name. This might be useful when Roundcube runs behind a reverse
// proxy using a subpath. This is a path part of the URL, not the full URL!
// The reverse proxy config can specify a custom header (e.g. X-Forwarded-Path) containing
// the path under which Roundcube is exposed to the outside world (e.g. /rcube/).
// This header value is then available in PHP with $_SERVER['HTTP_X_FORWARDED_PATH'].
// By default the path comes from  'REDIRECT_SCRIPT_URL', 'SCRIPT_NAME' or 'REQUEST_URI',
// whichever is set (in this order).
$config['request_path'] = null;

// Allows to define separate server/path for image/js/css files
// Warning: If the domain is different cross-domain access to some
// resources need to be allowed
// Sample:
//    <FilesMatch ".(eot|ttf|woff)">
//    Header set Access-Control-Allow-Origin "*"
//    </FilesMatch>
$config['assets_path'] = '';

// While assets_path is for the browser, assets_dir informs
// PHP code about the location of asset files in filesystem
$config['assets_dir'] = '';

// Options passed when creating Guzzle HTTP client, used to fetch remote content
// For example:
// [
//   'timeout' => 10,
//   'proxy' => 'tcp://localhost:8125',
// ]
$config['http_client'] = [];

// List of supported subject prefixes for a message reply
// This list is used to clean the subject when replying or sorting messages
$config['subject_reply_prefixes'] = ['Re:'];

// List of supported subject prefixes for a message forward
// This list is used to clean the subject when forwarding or sorting messages
$config['subject_forward_prefixes'] = ['Fwd:', 'Fw:'];

// Prefix to use in subject when replying to a message
$config['response_prefix'] = 'Re:';

// Prefix to use in subject when forwarding a message
$config['forward_prefix'] = 'Fwd:';

// ----------------------------------
// PLUGINS
// ----------------------------------

// List of active plugins (in plugins/ directory)
$config['plugins'] = [];

// ----------------------------------
// USER INTERFACE
// ----------------------------------

// default messages sort column. Use empty value for default server's sorting,
// or 'arrival', 'date', 'subject', 'from', 'to', 'fromto', 'size', 'cc'
$config['message_sort_col'] = '';

// default messages sort order
$config['message_sort_order'] = 'DESC';

// These cols are shown in the message list. Available cols are:
// subject, from, to, fromto, cc, replyto, date, size, status, flag, attachment, priority
$config['list_cols'] = ['subject', 'status', 'fromto', 'date', 'size', 'flag', 'attachment'];

// the default locale setting (leave empty for auto-detection)
// RFC1766 formatted language name like en_US, de_DE, de_CH, fr_FR, pt_BR
$config['language'] = null;

// use this format for date display (PHP DateTime format)
$config['date_format'] = 'Y-m-d';

// give this choice of date formats to the user to select from
// Note: do not use ambiguous formats like m/d/Y
$config['date_formats'] = ['Y-m-d', 'Y/m/d', 'Y.m.d', 'd-m-Y', 'd/m/Y', 'd.m.Y', 'j.n.Y'];

// use this format for time display (PHP DateTime format)
$config['time_format'] = 'H:i';

// give this choice of time formats to the user to select from
$config['time_formats'] = ['G:i', 'H:i', 'g:i a', 'h:i A'];

// use this format for short date display (derived from date_format and time_format)
$config['date_short'] = 'D H:i';

// use this format for detailed date/time formatting (derived from date_format and time_format)
$config['date_long'] = 'Y-m-d H:i';

// store draft message is this mailbox
// leave blank if draft messages should not be stored
// NOTE: Use folder names with namespace prefix (INBOX. on Courier-IMAP)
$config['drafts_mbox'] = 'Drafts';

// store spam messages in this mailbox
// NOTE: Use folder names with namespace prefix (INBOX. on Courier-IMAP)
$config['junk_mbox'] = 'Junk';

// store sent message is this mailbox
// leave blank if sent messages should not be stored
// NOTE: Use folder names with namespace prefix (INBOX. on Courier-IMAP)
$config['sent_mbox'] = 'Sent';

// move messages to this folder when deleting them
// leave blank if they should be deleted directly
// NOTE: Use folder names with namespace prefix (INBOX. on Courier-IMAP)
$config['trash_mbox'] = 'Trash';

// automatically create the above listed default folders on user login
$config['create_default_folders'] = false;

// protect the default folders from renames, deletes, and subscription changes
$config['protect_default_folders'] = true;

// Disable localization of the default folder names listed above
$config['show_real_foldernames'] = false;

// if in your system 0 quota means no limit set this option to true
$config['quota_zero_as_unlimited'] = false;

// Make use of the built-in spell checker.
$config['enable_spellcheck'] = false;

// Enables spellchecker exceptions dictionary.
// Setting it to 'shared' will make the dictionary shared by all users.
$config['spellcheck_dictionary'] = false;

// Set the spell checking engine. Possible values:
// - 'googie'  - the default (also used for connecting to Nox Spell Server, see 'spellcheck_uri' setting)
// - 'pspell'  - requires the PHP Pspell module and aspell installed
// - 'enchant' - requires the PHP Enchant module
// - 'atd'     - install your own After the Deadline server or check with the people at https://www.afterthedeadline.com before using their API
// Since Google shut down their public spell checking service, the default settings
// connect to https://spell.roundcube.net which is a hosted service provided by Roundcube.
// You can connect to any other googie-compliant service by setting 'spellcheck_uri' accordingly.
$config['spellcheck_engine'] = 'googie';

// For locally installed Nox Spell Server or After the Deadline services,
// please specify the URI to call it.
// Get Nox Spell Server from http://orangoo.com/labs/?page_id=72 or
// the After the Deadline package from https://www.afterthedeadline.com.
// Leave empty to use the public API of service.afterthedeadline.com
$config['spellcheck_uri'] = '';

// These languages can be selected for spell checking.
// Configure as a PHP style hash array: ['en'=>'English', 'de'=>'Deutsch'];
// Leave empty for default set of available language.
$config['spellcheck_languages'] = null;

// Makes that words with all letters capitalized will be ignored (e.g. GOOGLE)
$config['spellcheck_ignore_caps'] = false;

// Makes that words with numbers will be ignored (e.g. g00gle)
$config['spellcheck_ignore_nums'] = false;

// Makes that words with symbols will be ignored (e.g. g@@gle)
$config['spellcheck_ignore_syms'] = false;

// Number of lines at the end of a message considered to contain the signature.
// Increase this value if signatures are not properly detected and colored
$config['sig_max_lines'] = 15;

// don't let users set pagesize to more than this value if set
$config['max_pagesize'] = 200;

// Minimal value of user's 'refresh_interval' setting (in seconds)
$config['min_refresh_interval'] = 60;

// Specifies for how many seconds the Undo button will be available
// after object delete action. Currently used with supporting address book sources.
// Setting it to 0, disables the feature.
$config['undo_timeout'] = 0;

// A static list of canned responses which are immutable for the user
$config['compose_responses_static'] = [
//  ['name' => 'Canned Response 1', 'text' => 'Static Response One'],
//  ['name' => 'Canned Response 2', 'text' => 'Static Response Two'],
];

// List of HKP key servers for PGP public key lookups in Enigma/Mailvelope
// Note: Lookup is client-side, so the server must support Cross-Origin Resource Sharing
$config['keyservers'] = ['keys.openpgp.org'];

// Enables use of the Main Keyring in Mailvelope? If disabled, a per-site keyring
// will be used. This is set to false for backwards compatibility.
$config['mailvelope_main_keyring'] = false;

// Mailvelope RSA bit size for newly generated keys, either 2048 or 4096.
// It maybe desirable to use 2048 for sites with many mobile users.
$config['mailvelope_keysize'] = 4096;

// Html2Text link handling options:
// 0 - links will be removed
// 1 - a list of link URLs should be listed at the end of the text (default)
// 2 - link should be displayed to the original point in the text they appeared
$config['html2text_links'] = 1;

// Html2Text text width. Maximum width of the formatted text, in columns. Default: 75.
$config['html2text_width'] = 75;

// ----------------------------------
// ADDRESSBOOK SETTINGS
// ----------------------------------

// This indicates which type of address book to use. Possible choices:
// 'sql' - built-in sql addressbook enabled (default),
// ''    - built-in sql addressbook disabled.
//         Still LDAP or plugin-added addressbooks will be available.
//         BC Note: The value can actually be anything except 'sql', it does not matter.
$config['address_book_type'] = 'sql';

// In order to enable public ldap search, configure an array like the Verisign
// example further below. if you would like to test, simply uncomment the example.
// Array key must contain only safe characters, ie. a-zA-Z0-9_
$config['ldap_public'] = [];

// If you are going to use LDAP for individual address books, you will need to
// set 'user_specific' to true and use the variables to generate the appropriate DNs to access it.
//
// The recommended directory structure for LDAP is to store all the address book entries
// under the users main entry, e.g.:
//
//  o=root
//   ou=people
//    uid=user@domain
//  mail=contact@contactdomain
//
// So the base_dn would be uid=%fu,ou=people,o=root
// The bind_dn would be the same as based_dn or some super user login.
/*
 * example config for Verisign directory
 *
$config['ldap_public']['Verisign'] = [
  'name'          => 'Verisign.com',
  // Replacement variables supported in host names:
  // %h - user's IMAP hostname
  // %n - hostname ($_SERVER['SERVER_NAME'])
  // %t - hostname without the first part
  // %d - domain (http hostname $_SERVER['HTTP_HOST'] without the first part)
  // %z - IMAP domain (IMAP hostname without the first part)
  // For example %n = mail.domain.tld, %t = domain.tld
  // Note: Host can also be a full URI e.g. ldaps://hostname.local:636 (for SSL)
  // Note: If port number is omitted, it will be set to 636 (for ldaps://) or 389 otherwise.
  // Note: To enable TLS use tls:// prefix
  'hosts'         => array('directory.verisign.com:389'),
  'ldap_version'  => 3,       // using LDAPv3
  'network_timeout' => 10,    // The timeout (in seconds) for connect + bind attempts. This is only supported in PHP >= 5.3.0 with OpenLDAP 2.x
  'user_specific' => false,   // If true the base_dn, bind_dn and bind_pass default to the user's IMAP login.
  // When 'user_specific' is enabled following variables can be used in base_dn/bind_dn config:
  // %fu - The full username provided, assumes the username is an email
  //       address, uses the username_domain value if not an email address.
  // %u  - The username prior to the '@'.
  // %d  - The domain name after the '@'.
  // %dc - The domain name hierarchal string e.g. "dc=test,dc=domain,dc=com"
  // %dn - DN found by ldap search when search_filter/search_base_dn are used
  'base_dn'       => '',
  'bind_dn'       => '',
  'bind_pass'     => '',
  // It's possible to bind for an individual address book
  // The login name is used to search for the DN to bind with
  'search_base_dn' => '',
  'search_filter'  => '',   // e.g. '(&(objectClass=posixAccount)(uid=%u))'
  // DN and password to bind as before searching for bind DN, if anonymous search is not allowed
  'search_bind_dn' => '',
  'search_bind_pw' => '',
  // Base DN and filter used for resolving the user's domain root DN which feeds the %dc variables
  // Leave empty to skip this lookup and derive the root DN from the username domain
  'domain_base_dn' => '',
  'domain_filter'  => '',
  // Optional map of replacement strings => attributes used when binding for an individual address book
  'search_bind_attrib' => [],  // e.g. ['%udc' => 'ou']
  // Default for %dn variable if search doesn't return DN value
  'search_dn_default' => '',
  // Optional authentication identifier to be used as SASL authorization proxy
  // bind_dn need to be empty
  'auth_cid'       => '',
  // SASL authentication method (for proxy auth), e.g. DIGEST-MD5
  'auth_method'    => '',
  // Indicates if the addressbook shall be hidden from the list.
  // With this option enabled you can still search/view contacts.
  'hidden'        => false,
  // Indicates if the addressbook shall not list contacts but only allows searching.
  'searchonly'    => false,
  // Indicates if we can write to the LDAP directory or not.
  // If writable is true then these fields need to be populated:
  // LDAP_Object_Classes, required_fields, LDAP_rdn
  'writable'       => false,
  // To create a new contact these are the object classes to specify
  // (or any other classes you wish to use).
  'LDAP_Object_Classes' => ['top', 'inetOrgPerson'],
  // The RDN field that is used for new entries, this field needs
  // to be one of the search_fields, the base of base_dn is appended
  // to the RDN to insert into the LDAP directory.
  'LDAP_rdn'       => 'cn',
  // The required attributes needed to build a new contact as required by
  // the object classes (can include additional fields not required by the object classes).
  'required_fields' => ['cn', 'sn', 'mail'],
  // The attributes used when searching with "All fields" option
  // If empty, attributes for name, surname, firstname and email fields will be used
  'search_fields'   => ['mail', 'cn'],
  // mapping of contact fields to directory attributes
  //   1. for every attribute one can specify the number of values (limit) allowed.
  //      default is 1, a wildcard * means unlimited
  //   2. another possible parameter is separator character for composite fields
  //   3. it's possible to define field format for write operations, e.g. for date fields
  //      example: 'birthday:date[YmdHis\\Z]'
  'fieldmap' => [
    // Roundcube  => LDAP:limit
    'name'        => 'cn',
    'surname'     => 'sn',
    'firstname'   => 'givenName',
    'jobtitle'    => 'title',
    'email'       => 'mail:*',
    'phone:home'  => 'homePhone',
    'phone:work'  => 'telephoneNumber',
    'phone:mobile' => 'mobile',
    'phone:pager' => 'pager',
    'phone:workfax' => 'facsimileTelephoneNumber',
    'street'      => 'street',
    'zipcode'     => 'postalCode',
    'region'      => 'st',
    'locality'    => 'l',
    // if you country is a complex object, you need to configure 'sub_fields' below
    'country'      => 'c',
    'organization' => 'o',
    'department'   => 'ou',
    'jobtitle'     => 'title',
    'notes'        => 'description',
    'photo'        => 'jpegPhoto',
    // these currently don't work:
    // 'manager'       => 'manager',
    // 'assistant'     => 'secretary',
  ],
  // Map of contact sub-objects (attribute name => objectClass(es)), e.g. 'c' => 'country'
  'sub_fields' => [],
  // Generate values for the following LDAP attributes automatically when creating a new record
  'autovalues' => [
    // 'uid'  => 'md5(microtime())',               // You may specify PHP code snippets which are then eval'ed
    // 'mail' => '{givenname}.{sn}@mydomain.com',  // or composite strings with placeholders for existing attributes
  ],
  'sort'           => 'cn',         // The field to sort the listing by.
  'scope'          => 'sub',        // search mode: sub|base|list
  'filter'         => '(objectClass=inetOrgPerson)',      // used for basic listing (if not empty) and will be &'d with search queries. example: status=act
  'fuzzy_search'   => true,         // server allows wildcard search
  'vlv'            => false,        // Enable Virtual List View to more efficiently fetch paginated data (if server supports it)
  'vlv_search'     => false,        // Use Virtual List View functions for autocompletion searches (if server supports it)
  'numsub_filter'  => '(objectClass=organizationalUnit)',   // with VLV, we also use numSubOrdinates to query the total number of records. Set this filter to get all numSubOrdinates attributes for counting
  'config_root_dn' => 'cn=config',  // Root DN to search config entries (e.g. vlv indexes)
  'sizelimit'      => '0',          // Enables you to limit the count of entries fetched. Setting this to 0 means no limit.
  'timelimit'      => '0',          // Sets the number of seconds how long is spend on the search. Setting this to 0 means no limit.
  'referrals'      => false,        // Sets the LDAP_OPT_REFERRALS option. Mostly used in multi-domain Active Directory setups
  'dereference'    => 0,            // Sets the LDAP_OPT_DEREF option. One of: LDAP_DEREF_NEVER, LDAP_DEREF_SEARCHING, LDAP_DEREF_FINDING, LDAP_DEREF_ALWAYS
                                    // Used where addressbook contains aliases to objects elsewhere in the LDAP tree.

  // definition for contact groups (uncomment if no groups are supported)
  // for the groups base_dn, the user replacements %fu, %u, %d and %dc work as for base_dn (see above)
  // if the groups base_dn is empty, the contact base_dn is used for the groups as well
  // -> in this case, assure that groups and contacts are separated due to the concerning filters!
  'groups'  => [
    'base_dn'           => '',
    'scope'             => 'sub',       // Search mode: sub|base|list
    'filter'            => '(objectClass=groupOfNames)',
    'object_classes'    => ['top', 'groupOfNames'],   // Object classes to be assigned to new groups
    'member_attr'       => 'member',   // Name of the default member attribute, e.g. uniqueMember
    'name_attr'         => 'cn',       // Attribute to be used as group name
    'email_attr'        => 'mail',     // Group email address attribute (e.g. for mailing lists)
    'member_filter'     => '(objectclass=*)',  // Optional filter to use when querying for group members
    'vlv'               => false,      // Use VLV controls to list groups
    'class_member_attr' => [      // Mapping of group object class to member attribute used in these objects
      'groupofnames'       => 'member',
      'groupofuniquenames' => 'uniquemember'
    ],
  ],
  // this configuration replaces the regular groups listing in the directory tree with
  // a hard-coded list of groups, each listing entries with the configured base DN and filter.
  // if the 'groups' option from above is set, it'll be shown as the first entry with the name 'Groups'
  'group_filters' => [
    'departments' => [
      'name'    => 'Company Departments',
      'scope'   => 'list',
      'base_dn' => 'ou=Groups,dc=mydomain,dc=com',
      'filter'  => '(|(objectclass=groupofuniquenames)(objectclass=groupofurls))',
      'name_attr' => 'cn',
    ],
    'customers' => [
      'name'    => 'Customers',
      'scope'   => 'sub',
      'base_dn' => 'ou=Customers,dc=mydomain,dc=com',
      'filter'  => '(objectClass=inetOrgPerson)',
      'name_attr' => 'sn',
    ],
  ],
];
*/

// An ordered array of the ids of the addressbooks that should be searched
// when populating address autocomplete fields server-side. ex: ['sql','Verisign'];
$config['autocomplete_addressbooks'] = ['sql'];

// The minimum number of characters required to be typed in an autocomplete field
// before address books will be searched. Most useful for LDAP directories that
// may need to do lengthy results building given overly-broad searches
$config['autocomplete_min_length'] = 1;

// Number of parallel autocomplete requests.
// If there's more than one address book, n parallel (async) requests will be created,
// where each request will search in one address book. By default (0), all address
// books are searched in one request.
$config['autocomplete_threads'] = 0;

// Max. number of entries in autocomplete popup. Default: 15.
$config['autocomplete_max'] = 15;

// show address fields in this order
// available placeholders: {street}, {locality}, {zipcode}, {country}, {region}
$config['address_template'] = '{street}<br/>{locality} {zipcode}<br/>{country} {region}';

// Matching mode for addressbook search (including autocompletion)
// 0 - partial (*abc*), default
// 1 - strict (abc)
// 2 - prefix (abc*)
// Note: For LDAP sources fuzzy_search must be enabled to use 'partial' or 'prefix' mode
$config['addressbook_search_mode'] = 0;

// List of fields used on contacts list and for autocompletion searches
// Warning: These are field names not LDAP attributes (see 'fieldmap' setting)!
$config['contactlist_fields'] = ['name', 'firstname', 'surname', 'email'];

// Template of contact entry on the autocompletion list.
// You can use contact fields as: name, email, organization, department, etc.
// See program/actions/contacts/index.php for a list
$config['contact_search_name'] = '{name} <{email}>';

// Contact mode. If your contacts are mostly business, switch it to 'business'.
// This will prioritize form fields related to 'work' (instead of 'home').
// Default: 'private'.
$config['contact_form_mode'] = 'private';

// The addressbook source to store automatically collected recipients in.
// Default: true (the built-in "Collected recipients" addressbook, source id = '1')
// Note: It can be set to any writeable addressbook, e.g. 'sql'
$config['collected_recipients'] = true;

// The addressbook source to store trusted senders in.
// Default: true (the built-in "Trusted senders" addressbook, source id = '2')
// Note: It can be set to any writeable addressbook, e.g. 'sql'
$config['collected_senders'] = true;


// ----------------------------------
// USER PREFERENCES
// ----------------------------------

// Use this charset as fallback for message decoding
$config['default_charset'] = 'ISO-8859-1';

// Skin name: folder from skins/
$config['skin'] = 'elastic';

// Limit skins available for the user.
// Note: When not empty, it should include the default skin set in 'skin' option.
$config['skins_allowed'] = [];

// Enables using standard browser windows (that can be handled as tabs)
// instead of popup windows
$config['standard_windows'] = false;

// show up to X items in messages list view
$config['mail_pagesize'] = 50;

// show up to X items in contacts list view
$config['addressbook_pagesize'] = 50;

// sort contacts by this col (preferably either one of name, firstname, surname)
$config['addressbook_sort_col'] = 'surname';

// The way how contact names are displayed in the list.
// 0: prefix firstname middlename surname suffix (only if display name is not set)
// 1: firstname middlename surname
// 2: surname firstname middlename
// 3: surname, firstname middlename
$config['addressbook_name_listing'] = 0;

// use this timezone to display date/time
// valid timezone identifiers are listed here: php.net/manual/en/timezones.php
// 'auto' will use the browser's timezone settings
$config['timezone'] = 'auto';

// prefer displaying HTML messages
$config['prefer_html'] = true;

// Display remote resources (inline images, styles) in HTML messages. Default: 0.
// 0 - Never, always ask
// 1 - Allow from my contacts (all writeable addressbooks + collected senders and recipients)
// 2 - Always allow
// 3 - Allow from trusted senders only
$config['show_images'] = 0;

// open messages in new window
$config['message_extwin'] = false;

// open message compose form in new window
$config['compose_extwin'] = false;

// compose html formatted messages by default
//  0 - never,
//  1 - always,
//  2 - on reply to HTML message,
//  3 - on forward or reply to HTML message
//  4 - always, except when replying to plain text message
$config['htmleditor'] = 0;

// save copies of compose messages in the browser's local storage
// for recovery in case of browser crashes and session timeout.
$config['compose_save_localstorage'] = true;

// show pretty dates as standard
$config['prettydate'] = true;

// save compose message every 300 seconds (5min)
$config['draft_autosave'] = 300;

// Interface layout. Default: 'widescreen'.
//  'widescreen' - three columns
//  'desktop'    - two columns, preview on bottom
//  'list'       - two columns, no preview
$config['layout'] = 'widescreen';

// Mark as read when viewing a message (delay in seconds)
// Set to -1 if messages should not be marked as read
$config['mail_read_time'] = 0;

// Clear Trash on logout. Remove all messages or only older than N days.
// Supported values: false, true, 30, 60, 90. Default: false.
$config['logout_purge'] = false;

// Compact INBOX on logout
$config['logout_expunge'] = false;

// Display attached images below the message body
$config['inline_images'] = true;

// Encoding of long/non-ascii attachment names:
// 0 - Full RFC 2231 compatible
// 1 - RFC 2047 for 'name' and RFC 2231 for 'filename' parameter (Thunderbird's default)
// 2 - Full 2047 compatible
$config['mime_param_folding'] = 1;

// Set true if deleted messages should not be displayed
// This will make the application run slower
$config['skip_deleted'] = false;

// Set true to Mark deleted messages as read as well as deleted
// False means that a message's read status is not affected by marking it as deleted
$config['read_when_deleted'] = true;

// Set to true to never delete messages immediately
// Use 'Purge' to remove messages marked as deleted
$config['flag_for_deletion'] = false;

// Default interval for auto-refresh requests (in seconds)
// These are requests for system state updates e.g. checking for new messages, etc.
// Setting it to 0 disables the feature.
$config['refresh_interval'] = 60;

// If true all folders will be checked for recent messages
$config['check_all_folders'] = false;

// If true, after message/contact delete/move, the next message/contact will be displayed
$config['display_next'] = true;

// Default messages listing mode. One of 'threads' or 'list'.
$config['default_list_mode'] = 'list';

// 0 - Do not expand threads
// 1 - Expand all threads automatically
// 2 - Expand only threads with unread messages
$config['autoexpand_threads'] = 0;

// When replying:
// -1 - don't cite the original message
// 0  - place cursor below the original message
// 1  - place cursor above original message (top posting)
// 2  - place cursor above original message (top posting), but do not indent the quote
$config['reply_mode'] = 0;

// When replying strip original signature from message
$config['strip_existing_sig'] = true;

// Show signature:
// 0 - Never
// 1 - Always
// 2 - New messages only
// 3 - Forwards and Replies only
$config['show_sig'] = 1;

// By default the signature is placed depending on cursor position (reply_mode).
// Sometimes it might be convenient to start the reply on top but keep
// the signature below the quoted text (sig_below = true).
$config['sig_below'] = false;

// Enables adding of standard separator to the signature
$config['sig_separator'] = true;

// Use MIME encoding (quoted-printable) for 8bit characters in message body
$config['force_7bit'] = false;

// Default fields configuration for mail search.
// The array can contain a per-folder list of header fields which should be considered when searching
// The entry with key '*' stands for all folders which do not have a specific list set.
// Supported fields: subject, from, to, cc, bcc, replyto, followupto, body, text.
// Please note that folder names should to be in sync with $config['*_mbox'] options
$config['search_mods'] = null;  // Example: ['*' => ['subject'=>1, 'from'=>1], 'Sent' => ['subject'=>1, 'to'=>1]];

// Defaults of the addressbook search field configuration.
$config['addressbook_search_mods'] = null;  // Example: ['name'=>1, 'firstname'=>1, 'surname'=>1, 'email'=>1, '*'=>1];

// Directly delete messages in Junk instead of moving to Trash
$config['delete_junk'] = false;

// Behavior if a received message requests a message delivery notification (read receipt)
// 0 = ask the user,
// 1 = send automatically,
// 2 = ignore (never send or ask)
// 3 = send automatically if sender is in my contacts, otherwise ask the user
// 4 = send automatically if sender is in my contacts, otherwise ignore
// 5 = send automatically if sender is a trusted sender, otherwise ask the user
// 6 = send automatically if sender is a trusted sender, otherwise ignore
$config['mdn_requests'] = 0;

// Return receipt checkbox default state
$config['mdn_default'] = 0;

// Delivery Status Notification checkbox default state
$config['dsn_default'] = 0;

// Place replies in the folder of the message being replied to
$config['reply_same_folder'] = false;

// Sets default mode of Forward feature to "forward as attachment"
$config['forward_attachment'] = false;

// Defines address book (internal index) to which new contacts will be added
// By default it is the first writeable addressbook.
// Note: Use '0' for built-in address book.
$config['default_addressbook'] = null;

// Enables spell checking before sending a message.
$config['spellcheck_before_send'] = false;

// Skip alternative email addresses in autocompletion (show one address per contact)
$config['autocomplete_single'] = false;

// Default font for composed HTML message.
// Supported values: Andale Mono, Arial, Arial Black, Book Antiqua, Courier New,
// Georgia, Helvetica, Impact, Tahoma, Terminal, Times New Roman, Trebuchet MS, Verdana
$config['default_font'] = 'Verdana';

// Default font size for composed HTML message.
// Supported sizes: 8pt, 10pt, 12pt, 14pt, 18pt, 24pt, 36pt
$config['default_font_size'] = '10pt';

// Enables display of email address with name instead of a name (and address in title)
$config['message_show_email'] = false;

// Default behavior of Reply-All button:
// 0 - Reply-All always
// 1 - Reply-List if mailing list is detected
$config['reply_all_mode'] = 0;
