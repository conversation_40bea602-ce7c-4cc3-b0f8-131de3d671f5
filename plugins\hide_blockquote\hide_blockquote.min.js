/**
 * Hide Blockquotes plugin script
 *
 * @licstart  The following is the entire license notice for the
 * JavaScript code in this file.
 *
 * Copyright (c) The Roundcube Dev Team
 *
 * The JavaScript code in this page is free software: you can redistribute it
 * and/or modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version.
 *
 * @licend  The above is the entire license notice
 * for the JavaScript code in this file.
 */
function hide_blockquote(){var t=rcmail.env.blockquote_limit;t<=0||$("div.message-part div.pre > blockquote",$("#messagebody")).each(function(){var e,i=$(this);$("blockquote").before(document.createTextNode("\n")),(e=i.text().trim().split(/\n/)).length<=t&&i.height()<=15*t||(e=$('<blockquote class="blockquote-header">').css({"white-space":"nowrap",overflow:"hidden",position:"relative"}).text(e[0]),$('<span class="blockquote-link"></span>').css({position:"absolute","z-Index":2}).text(rcmail.get_label("hide_blockquote.show")).data("parent",e).click(function(){var e=$(this),t=e.data("parent"),o=t.is(":visible");e.text(rcmail.get_label(o?"hide":"show","hide_blockquote")).detach().appendTo(o?i:t).toggleClass("collapsed"),t[o?"hide":"show"](),i[o?"show":"hide"]()}).appendTo(e),i.hide().css({position:"relative"}).before(e))})}window.rcmail&&rcmail.addEventListener("init",function(){hide_blockquote()});
