<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = '封存';
$labels['buttontitle'] = '封存此信件';
$labels['archived'] = '成功封存';
$labels['archivedreload'] = '封存動作完成。要重新載入頁面，才能看到新的封存資料夾';
$labels['archiveerror'] = '部分信件無法完成封存';
$labels['archivefolder'] = '封存';
$labels['settingstitle'] = '封存';
$labels['archivetype'] = '封存檔案切割方式：';
$labels['archivetypeyear'] = '年分 (例如: 封存/2012)';
$labels['archivetypemonth'] = '月分 (例如: 封存/2012/06)';
$labels['archivetypetbmonth'] = '月分 - 相容於 Thunderbird (例如: 封存/2012/2012-06)';
$labels['archivetypefolder'] = '原始資料夾';
$labels['archivetypefolderyear'] = '年份與原始資料夾（例如：Archive/2012/...）';
$labels['archivetypefoldermonth'] = '年份、月份與原始資料夾（例如：Archive/2012/06/...）';
$labels['archivetypesender'] = '寄件者電子信箱';
$labels['unkownsender'] = '未知';
$labels['readonarchive'] = '將封存的郵件標示為已讀取';
