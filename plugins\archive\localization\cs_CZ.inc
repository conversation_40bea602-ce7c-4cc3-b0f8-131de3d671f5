<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = 'Archiv';
$labels['buttontitle'] = 'Archivovat zprávu';
$labels['archived'] = 'Vloženo do archivu';
$labels['archivedreload'] = 'Archivováno. Obnovte stránku, abyste uviděli nové složky v archivu.';
$labels['archiveerror'] = 'Některé zprávy nelze archivovat';
$labels['archivefolder'] = 'Archiv';
$labels['settingstitle'] = 'Archiv';
$labels['archivetype'] = 'Rozdělit archiv podle';
$labels['archivetypeyear'] = 'Rok (např.  Archiv/2012)';
$labels['archivetypemonth'] = 'Měsíc (např. Archiv/2012/06)';
$labels['archivetypetbmonth'] = 'Měsíc - kompatibilní s Thunderbird klientem (např. Archiv/2012/2012-06)';
$labels['archivetypefolder'] = 'Původní složka';
$labels['archivetypefolderyear'] = 'Rok a původní složka (např. Archive/2012/...)';
$labels['archivetypefoldermonth'] = 'Rok, měsíc a původní složka (např. Archive/2012/06/...)';
$labels['archivetypesender'] = 'E-mail odesílatele';
$labels['unkownsender'] = 'neznámý';
$labels['readonarchive'] = 'Při archivaci označit zprávu jako přečtenou';
