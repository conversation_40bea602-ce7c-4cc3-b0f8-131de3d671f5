{"name": "roundcube/filesystem_attachments", "type": "roundcube-plugin", "description": "This is a core plugin which provides basic, filesystem based attachment temporary file handling. This includes storing attachments of messages currently being composed, writing attachments to disk when drafts with attachments are re-opened and writing attachments to disk for inline display in current html compositions.", "license": "GPL-3.0-or-later", "version": "1.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "repositories": [{"type": "composer", "url": "https://plugins.roundcube.net"}], "require": {"php": ">=7.3.0", "roundcube/plugin-installer": ">=0.1.3"}}