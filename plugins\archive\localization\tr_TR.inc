<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = 'Arşivle';
$labels['buttontitle'] = 'Bu iletiyi arşivle';
$labels['archived'] = 'Arşivlendi.';
$labels['archivedreload'] = 'Arşivlendi. Yeni arşiv klasörlerini görmek için sayfayı yenileyin.';
$labels['archiveerror'] = 'Bazı iletiler arşivlenemedi';
$labels['archivefolder'] = 'Arşiv';
$labels['settingstitle'] = 'Arşiv';
$labels['archivetype'] = 'Arşivleme şuna göre yapılsın';
$labels['archivetypeyear'] = 'Yıl (Arşiv/2012)';
$labels['archivetypemonth'] = 'Ay (Arşiv/2012/06)';
$labels['archivetypetbmonth'] = 'Ay - Thunderbird uyumlu (Arsiv2012/2012-06 gibi)';
$labels['archivetypefolder'] = 'Özgün klasör';
$labels['archivetypefolderyear'] = 'Yıl ve özgün klasör (Örnek: Arşiv/2012 ...)';
$labels['archivetypefoldermonth'] = 'Yıl, ay ve özgün klasör (Örnek: Arşiv/2012/06 ...)';
$labels['archivetypesender'] = 'Gönderici adresi';
$labels['unkownsender'] = 'bilinmeyen';
$labels['readonarchive'] = 'Arşivdeki ileti okunmuş olarak işaretlensin';
