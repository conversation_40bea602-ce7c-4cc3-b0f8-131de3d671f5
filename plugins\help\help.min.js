/**
 * Help plugin client script
 *
 * @licstart  The following is the entire license notice for the
 * JavaScript code in this file.
 *
 * Copyright (c) The Roundcube Dev Team
 *
 * The JavaScript code in this page is free software: you can redistribute it
 * and/or modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version.
 *
 * @licend  The above is the entire license notice
 * for the JavaScript code in this file.
 */
function show_help_content(e,n){var a,i=window,r=rcmail.env.help_links[e];return(a=rcmail.get_frame_window(rcmail.env.contentframe))&&(i=a,r+=(-1<r.indexOf("?")?"&":"?")+"_framed=1"),rcmail.env.extwin&&(r+=(-1<r.indexOf("?")?"&":"?")+"_extwin=1"),/^self/.test(r)&&(r=r.substr(4)+"&_content=1&_task=help&_action="+e),rcmail.env.help_action_item=n?n.target:$('[rel="'+e+'"]'),rcmail.show_contentframe(!0),rcmail.location_href(r,i,!0),!1}window.rcmail&&(rcmail.addEventListener("beforeswitch-task",function(e){if("help"==e)return"help"!=rcmail.task&&(e=rcmail.url("help/index",{_rel:rcmail.task+(rcmail.env.action?"/"+rcmail.env.action:"")}),rcmail.env.help_open_extwin?rcmail.open_window(e,1020,!1):rcmail.redirect(e,!1)),!1}),rcmail.addEventListener("init",function(e){if(rcmail.env.contentframe&&"help"==rcmail.task){$("#"+rcmail.env.contentframe).on("load error",function(e){rcmail.set_busy(!1,null,rcmail.env.frame_lock),rcmail.env.frame_lock=null,"load"==e.type&&($(rcmail.env.help_action_item).parents("ul").children().removeClass("selected"),$(rcmail.env.help_action_item).parent().addClass("selected"))});try{var n=rcmail.get_frame_window(rcmail.env.contentframe);n&&0<=n.location.href.indexOf(rcmail.env.blankpage)&&show_help_content(rcmail.env.action)}catch(e){}}}));
