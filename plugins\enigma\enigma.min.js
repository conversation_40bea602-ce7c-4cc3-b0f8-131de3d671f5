/**
 * Enigma plugin script
 *
 * @licstart  The following is the entire license notice for the
 * JavaScript code in this file.
 *
 * Copyright (c) The Roundcube Dev Team
 *
 * The JavaScript code in this page is free software: you can redistribute it
 * and/or modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version.
 *
 * @licend  The above is the entire license notice
 * for the JavaScript code in this file.
 */
window.rcmail&&rcmail.addEventListener("init",function(e){"settings"==rcmail.env.task?(rcmail.gui_objects.keyslist&&(rcmail.keys_list=new rcube_list_widget(rcmail.gui_objects.keyslist,{multiselect:!0,draggable:!1,keyboard:!0}),rcmail.keys_list.addEventListener("select",function(e){rcmail.enigma_keylist_select(e)}).addEventListener("keypress",function(e){rcmail.list_keypress(e,{del:"plugin.enigma-key-delete"})}).init().focus(),rcmail.enigma_list(),rcmail.register_command("firstpage",function(e){return rcmail.enigma_list_page("first")}),rcmail.register_command("previouspage",function(e){return rcmail.enigma_list_page("previous")}),rcmail.register_command("nextpage",function(e){return rcmail.enigma_list_page("next")}),rcmail.register_command("lastpage",function(e){return rcmail.enigma_list_page("last")})),"plugin.enigmakeys"==rcmail.env.action&&(rcmail.register_command("search",function(e){return rcmail.enigma_search(e)},!0),rcmail.register_command("reset-search",function(e){return rcmail.enigma_search_reset(e)},!0),rcmail.register_command("plugin.enigma-import",function(){rcmail.enigma_import()},!0),rcmail.register_command("plugin.enigma-import-search",function(){rcmail.enigma_import_search()},!0),rcmail.register_command("plugin.enigma-key-export",function(){rcmail.enigma_export()}),rcmail.register_command("plugin.enigma-key-export-selected",function(){rcmail.enigma_export(!0)}),rcmail.register_command("plugin.enigma-key-import",function(){rcmail.enigma_key_import()},!0),rcmail.register_command("plugin.enigma-key-import-search",function(){rcmail.enigma_key_import_search()},!0),rcmail.register_command("plugin.enigma-key-delete",function(e){return rcmail.enigma_delete()}),rcmail.register_command("plugin.enigma-key-create",function(e){return rcmail.enigma_key_create()},!0),rcmail.register_command("plugin.enigma-key-save",function(e){return rcmail.enigma_key_create_save()},!0),rcmail.addEventListener("responseafterplugin.enigmakeys",function(){rcmail.enable_command("plugin.enigma-key-export",0<rcmail.env.rowcount),rcmail.triggerEvent("listupdate",{list:rcmail.keys_list,rowcount:rcmail.env.rowcount})}),rcmail.gui_objects.importform)&&$("#rcmimportsearch").keydown(function(e){if(13==e.which)return rcmail.enigma_import_search(),!1})):"mail"==rcmail.env.task&&("compose"==rcmail.env.action&&(rcmail.addEventListener("beforesend",function(e){rcmail.enigma_beforesend_handler(e)}).addEventListener("beforesavedraft",function(e){rcmail.enigma_beforesavedraft_handler(e)}),$("#enigmamenu").find("input,label").mouseup(function(e){e.stopPropagation()}),$("a.button.enigma").prop("tabindex",$("#messagetoolbar > a").first().prop("tabindex")),$.each(["encrypt","sign"],function(){var e=this,t=$("#enigma"+e+"opt");rcmail.env["enigma_force_"+e]&&t.prop("checked",!0),window.UI&&UI.compose_status&&t.on("change",function(){UI.compose_status(e,this.checked)}),t.trigger("change")})),rcmail.env.enigma_password_request)&&rcmail.enigma_password_request(rcmail.env.enigma_password_request)}),rcube_webmail.prototype.enigma_key_import=function(){var t=$("<iframe>").attr("src",this.url("plugin.enigmakeys",{_a:"import",_framed:1}));this.enigma_import_dialog=this.simple_dialog(t,"enigma.importkeys",function(e){t[0].contentWindow.rcmail.enigma_import()},{button:"import",width:500,height:180})},rcube_webmail.prototype.enigma_key_import_search=function(){var e=$("<iframe>").attr("src",this.url("plugin.enigmakeys",{_a:"import-search",_framed:1}));this.enigma_import_dialog=this.simple_dialog(e,"enigma.keyimportsearchlabel",function(){e[0].contentWindow.rcmail.enigma_import_search()},{button:"search",width:500,height:150})},rcube_webmail.prototype.enigma_import_success=function(){(this.enigma_import_dialog||parent.rcmail.enigma_import_dialog).dialog("destroy")},rcube_webmail.prototype.enigma_key_create=function(){this.keys_list.clear_selection(),this.enigma_loadframe("&_action=plugin.enigmakeys&_a=create&_nav=hide")},rcube_webmail.prototype.enigma_key_create_save=function(){var t,e=[],i=$("#key-pass").val(),a=$("#key-pass-confirm").val(),n=$("#key-type").val();$('[name="identity[]"]:checked').each(function(){e.push({name:$(this).data("name")||"",email:$(this).data("email")})}),i&&a?i!=a?this.alert_dialog(this.get_label("enigma.passwordsdiffer")):e.length?window.openpgp&&window.crypto&&window.crypto.getRandomValues?(t=this.set_busy(!0,"enigma.keygenerating"),a={userIDs:e,passphrase:i,type:n.substring(0,3)},"ecc"==n?a.curve="ed25519":a.rsaBits="rsa4096"==n?4096:2048,openpgp.generateKey(a).then(function(e){e={_a:"import",_keys:e.privateKey,_generated:1,_passwd:i};rcmail.http_post("plugin.enigmakeys",e,t)},function(e){console.error(e),rcmail.set_busy(!1,null,t),rcmail.display_message(rcmail.get_label("enigma.keygenerateerror"),"error")})):rcmail.display_message(rcmail.get_label("enigma.keygennosupport"),"error"):this.alert_dialog(this.get_label("enigma.noidentselected")):this.alert_dialog(this.get_label("enigma.formerror"))},rcube_webmail.prototype.enigma_key_create_success=function(){parent.rcmail.enigma_list(1)},rcube_webmail.prototype.enigma_delete=function(){var a=this.keys_list.get_selection();a.length&&this.confirm_dialog(this.get_label("enigma.keyremoveconfirm"),"delete",function(e,t){var i=t.display_message(t.get_label("enigma.keyremoving"),"loading");t.http_post("plugin.enigmakeys",{_a:"delete",_keys:a},i)})},rcube_webmail.prototype.enigma_export=function(e){var t=!1,i=this.keys_list,e=e?i.get_selection().join(","):"*",a={_keys:e};if(e.length){if("*"==e?t=!0:$.each(i.get_selection(),function(){if((flags=$(i.rows[this].obj).data("flags"))&&0<=flags.indexOf("p"))return!(t=!0)}),t)return this.show_popup_dialog(this.get_label("enigma.keyexportprompt"),this.get_label("enigma.exportkeys"),[{class:"export mainaction",text:this.get_label("enigma.onlypubkeys"),click:function(e){rcmail.enigma_export_submit(a),$(this).remove()}},{class:"export",text:this.get_label("enigma.withprivkeys"),click:function(e){a._priv=1,rcmail.enigma_export_submit(a),$(this).remove()}},{class:"cancel",text:this.get_label("close"),click:function(e){$(this).remove()}}],{width:500});this.enigma_export_submit(a)}},rcube_webmail.prototype.enigma_export_submit=function(e){var t="keyexport-"+(new Date).getTime(),i=$("<form>").attr({target:t,method:"post",style:"display:none",action:"?_action=plugin.enigmakeys&_task=settings&_a=export"}),t=$("<iframe>").attr({name:t,style:"display:none"});i.append($("<input>").attr({name:"_token",value:this.env.request_token})),$.each(e,function(e,t){i.append($("<input>").attr({name:e,value:t}))}),t.appendTo(document.body),i.appendTo(document.body).submit()},rcube_webmail.prototype.enigma_import=function(){var e,t,i="keyimport-"+(new Date).getTime();if(e=this.gui_objects.importform){if(!(t=document.getElementById("rcmimportfile"))||t.value)return t=this.set_busy(!0,"importwait"),$("<iframe>").attr({name:i,style:"display:none"}).appendTo(document.body),$(e).attr({target:i,action:this.add_url(e.action,"_unlock",t)}).submit(),!0;this.alert_dialog(this.get_label("selectimportfile"))}},rcube_webmail.prototype.enigma_import_search=function(){var e;this.gui_objects.importform&&(e=$("#rcmimportsearch").val())&&this.enigma_find_publickey(e)},rcube_webmail.prototype.enigma_keylist_select=function(e){var t,i=e.get_single_selection();i&&!e.multi_selecting&&(t="&_action=plugin.enigmakeys&_a=info&_id="+i),this.enigma_loadframe(t),this.enable_command("plugin.enigma-key-delete","plugin.enigma-key-export-selected",0<e.get_selection().length)},rcube_webmail.prototype.enigma_loadframe=function(e){var t;(t=this.get_frame_window(this.env.contentframe))&&(e?(this.env.frame_lock=this.set_busy(!0,"loading"),t.location.href=this.env.comm_path+"&_framed=1"+e):(t.location&&t.location.href.indexOf(this.env.blankpage)<0&&(t.location.href=this.env.blankpage),this.env.frame_lock&&this.set_busy(!1,null,this.env.frame_lock)))},rcube_webmail.prototype.enigma_search=function(e){var t;return((e=!e&&this.gui_objects.qsearchbox?this.gui_objects.qsearchbox.value:e)||this.env.search_request)&&(e={_a:"search",_q:e},t=this.set_busy(!0,"searching"),this.env.current_page=1,this.enigma_loadframe(),this.enigma_clear_list(),this.http_post("plugin.enigmakeys",e,t)),!1},rcube_webmail.prototype.enigma_search_reset=function(e){var t=this.env.search_request;return this.reset_qsearch(),t&&(this.enigma_loadframe(),this.enigma_clear_list(),this.enigma_list()),!1},rcube_webmail.prototype.enigma_list=function(e,t){if(this.is_framed())return parent.rcmail.enigma_list(e,t);var i={_a:"list"},a=this.set_busy(!0,"loading");this.env.current_page=e||1,this.env.search_request&&(i._q=this.env.search_request),e&&(i._p=e),this.enigma_clear_list(t),this.http_post("plugin.enigmakeys",i,a)},rcube_webmail.prototype.enigma_list_page=function(e){"next"==e?e=this.env.current_page+1:"last"==e?e=this.env.pagecount:"prev"==e&&1<this.env.current_page?e=this.env.current_page-1:"first"==e&&1<this.env.current_page&&(e=1),this.enigma_list(e)},rcube_webmail.prototype.enigma_clear_list=function(e){!1!==e&&this.enigma_loadframe(),this.keys_list&&this.keys_list.clear(!0),this.enable_command("plugin.enigma-key-delete","plugin.enigma-key-delete-selected",!1),this.triggerEvent("listupdate",{list:this.keys_list,rowcount:this.keys_list.rowcount})},rcube_webmail.prototype.enigma_add_list_row=function(e){if(!this.gui_objects.keyslist||!this.keys_list)return!1;var t=this.keys_list,i=(this.gui_objects.keyslist.tBodies[0].rows.length,document.createElement("tr")),a=document.createElement("td");i.id="rcmrow"+e.id,i.className="message",e.flags&&$(i).data("flags",e.flags),a.className="name",a.innerHTML=e.name,i.appendChild(a),t.insert_row(i)},rcube_webmail.prototype.enigma_beforesend_handler=function(e){this.env.last_action="send",this.enigma_compose_handler(e)},rcube_webmail.prototype.enigma_beforesavedraft_handler=function(e){this.env.last_action="savedraft",this.enigma_compose_handler(e)},rcube_webmail.prototype.enigma_compose_handler=function(e){var i=this.gui_objects.messageform;$("#enigmamenu input").each(function(){var e=this.id+"_cpy",t=$("#"+e);t.length||(t=$(this).clone()).prop({id:e,type:"hidden"}).appendTo(i),t.val(this.checked?"1":"")})},rcube_webmail.prototype.enigma_import_attachment=function(e){var t=this.set_busy(!0,"loading"),e={_uid:this.env.uid,_mbox:this.env.mailbox,_part:e};return this.http_post("plugin.enigmaimport",e,t),!1},rcube_webmail.prototype.enigma_password_request=function(t){var i,e,a,n,r;t&&t.keyid&&(e=(i=this).get_label("enigma.enterkeypass"),a=$('<div class="prompt">'),n=$('<p class="message">').appendTo(a),r=$("<input>").attr({type:"password",size:30,"data-submit":"true"}).appendTo(a),t.key=t.keyid,8<t.keyid.length&&(t.keyid=t.keyid.substr(t.keyid.length-8)),$.each(["keyid","user"],function(){e=e.replace("$"+this,t[this])}),n.text(e),this.show_popup_dialog(a,this.get_label("enigma.enterkeypasstitle"),[{text:this.get_label("ok"),class:"mainaction save unlock",click:function(e){e.stopPropagation();e=i.is_framed()?window.parent.$:$;t.password=r.val(),t.password?(i.enigma_password_submit(t),e(this).remove()):r.focus()}},{text:this.get_label("cancel"),class:"cancel",click:function(e){var t=i.is_framed()?window.parent.$:$;e.stopPropagation(),t(this).remove()}}],{width:400}),this.is_framed())&&parent.rcmail.message_list&&parent.rcmail.message_list.blur()},rcube_webmail.prototype.enigma_password_submit=function(e){var i,t;return"compose"!=this.env.action||e["compose-init"]?"plugin.enigmakeys"==this.env.action&&(i=this.gui_objects.importform)?($('input[name="_keyid"]',i).length||$(i).append($("<input>").attr({type:"hidden",name:"_keyid",value:e.key})).append($("<input>").attr({type:"hidden",name:"_passwd",value:e.password})),this.enigma_import()):(t=e.nolock?null:this.set_busy(!0,"loading"),i=$("<form>").attr({method:"post",action:e.action||location.href,style:"display:none"}).append($("<input>").attr({type:"hidden",name:"_keyid",value:e.key})).append($("<input>").attr({type:"hidden",name:"_passwd",value:e.password})).append($("<input>").attr({type:"hidden",name:"_token",value:this.env.request_token})).append($("<input>").attr({type:"hidden",name:"_unlock",value:t})),$.each(e,function(e,t){0==e.indexOf("input")&&i.append($("<input>").attr({type:"hidden",name:e.substring(5),value:t}))}),e.iframe&&(t="enigma_frame_"+(new Date).getTime(),$("<iframe>").attr({style:"display:none",name:t}).appendTo(document.body),i.attr("target",t)),void i.appendTo(document.body).submit()):this.enigma_password_compose_submit(e)},rcube_webmail.prototype.enigma_password_compose_submit=function(e){var t=this.gui_objects.messageform;$('input[name="_keyid"]',t).length?($('input[name="_keyid"]',t).val(e.key),$('input[name="_passwd"]',t).val(e.password)):$(t).append($("<input>").attr({type:"hidden",name:"_keyid",value:e.key})).append($("<input>").attr({type:"hidden",name:"_passwd",value:e.password})),this.submit_messageform("savedraft"==this.env.last_action)},rcube_webmail.prototype.enigma_key_not_found=function(e){var t=[{class:"mainaction search",text:e.button,click:function(){$(this).remove(),rcmail.enigma_find_publickey(e.email)}}];return"encrypt"==e.mode&&t.push({class:"send",text:rcmail.get_label("enigma.sendunencrypted"),click:function(e){$(this).remove(),$("#enigmaencryptopt").prop("checked",!1).change(),rcmail.command("send",{nocheck:!0},e.target,e.originalEvent)}}),t.push({class:"cancel",text:this.get_label("cancel"),click:function(){$(this).remove()}}),this.show_popup_dialog(e.text,e.title,t,{width:500,dialogClass:"error"})},rcube_webmail.prototype.enigma_find_publickey=function(e){this.mailvelope_search_pubkeys([e],function(e){},function(e){var t=rcmail.set_busy(!0,"enigma.importwait"),e={_a:"import",_keys:e};"plugin.enigmakeys"==rcmail.env.action&&(e._refresh=1),rcmail.http_post("plugin.enigmakeys",e,t)})};
