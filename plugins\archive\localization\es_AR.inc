<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = 'Archivo';
$labels['buttontitle'] = 'Archivar este mensaje';
$labels['archived'] = 'Mensaje Archivado';
$labels['archivedreload'] = 'Archivado satisfactoriamente. Recarga la página para ver las nuevas capetas archivadas.';
$labels['archiveerror'] = 'Algunos mensajes no pudieron archivarse';
$labels['archivefolder'] = 'Archivo';
$labels['settingstitle'] = 'Archivo';
$labels['archivetype'] = 'Separar archivo por';
$labels['archivetypeyear'] = 'Año (ej. Archivo/2012)';
$labels['archivetypemonth'] = 'Mes (ej. Archivo/2012/06)';
$labels['archivetypetbmonth'] = 'Mes - estilo Thunderbird  (ej. Archive/2012/2012-06)';
$labels['archivetypefolder'] = 'Carpeta original';
$labels['archivetypefolderyear'] = 'Año y la carpeta original (p.ej. Archivo/2012/...)';
$labels['archivetypefoldermonth'] = 'Año, mes y la carpeta original (p.ej. Archivo/2012/06/...)';
$labels['archivetypesender'] = 'Remitente del correo';
$labels['unkownsender'] = 'desconocido';
$labels['readonarchive'] = 'Marcar el mensaje como leído al archivar';
