<?php

/*
 +-----------------------------------------------------------------------+
 | Localization file of the Roundcube Webmail Archive plugin             |
 |                                                                       |
 | Copyright (C) The Roundcube Dev Team                                  |
 |                                                                       |
 | Licensed under the GNU General Public License version 3 or            |
 | any later version with exceptions for skins & plugins.                |
 | See the README file for a full license statement.                     |
 +-----------------------------------------------------------------------+

 For translation see https://www.transifex.com/projects/p/roundcube-webmail/resource/plugin-archive/
*/

$labels['buttontext'] = 'Archiválás';
$labels['buttontitle'] = 'Üzenet archiválása';
$labels['archived'] = 'Sikeres archiválás';
$labels['archivedreload'] = 'Az arhiválás sikeres. Frissitsd az oldalt, hogy lásd a létrejött arhivum mappákat.';
$labels['archiveerror'] = 'Néhány üzenetet nem sikerült arhiválni';
$labels['archivefolder'] = 'Archiválás';
$labels['settingstitle'] = 'Archiválás';
$labels['archivetype'] = 'Arhívum tovább bontása a következő szerint';
$labels['archivetypeyear'] = 'Év ( pl Arhívum/2012)';
$labels['archivetypemonth'] = 'Honap ( pl Arhívum/2012/06)';
$labels['archivetypetbmonth'] = 'Honap - Thunderbird kompatibilis ( pl Arhívum/2012/2012-06)';
$labels['archivetypefolder'] = 'Eredeti mappa';
$labels['archivetypefolderyear'] = 'Év és az eredeti mappa (pl: Archív/2012/...)';
$labels['archivetypefoldermonth'] = 'Év, hónap és az eredeti mappa  (pl: Archív/2012/06/...)';
$labels['archivetypesender'] = 'Feladó';
$labels['unkownsender'] = 'ismeretlen';
$labels['readonarchive'] = 'Üzenet olvasottként jelölése arhiváláskor';
