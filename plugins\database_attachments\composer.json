{"name": "roundcube/database_attachments", "type": "roundcube-plugin", "description": "This plugin which provides database backed storage for temporary attachment file handling. The primary advantage of this plugin is its compatibility with round-robin dns multi-server Roundcube installations.", "license": "GPL-3.0-or-later", "version": "1.2", "authors": [{"name": "<PERSON>ek<PERSON><PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "repositories": [{"type": "composer", "url": "https://plugins.roundcube.net"}], "require": {"php": ">=7.3.0", "roundcube/plugin-installer": ">=0.1.3", "roundcube/filesystem_attachments": ">=1.0.0"}}